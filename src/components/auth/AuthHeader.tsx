"use client";
import { motion } from "framer-motion";

interface AuthHeaderProps {
  title: string;
  subtitle?: string;
  showLogo?: boolean;
  logoType?: "dots" | "icon";
}

const AuthHeader = ({ 
  title, 
  subtitle, 
  showLogo = true,
  logoType = "dots"
}: AuthHeaderProps) => {
  return (
    <>
      {/* Logo Section */}
      {showLogo && (
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-center my-4 sm:my-6 lg:my-8 bg-transparent"
        >
          <img
            src="/images/dot-l.png"
            alt="Left Image"
            className="w-12 h-8 sm:w-16 sm:h-10 lg:w-20 lg:h-12 bg-transparent"
          />
          
          {logoType === "icon" && (
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <img
                src="/images/icon.png"
                alt="Logo"
                className="w-12 h-8 sm:w-16 sm:h-10 lg:w-20 lg:h-12 bg-transparent"
              />
            </motion.div>
          )}
          
          <img
            src="/images/dot-r.png"
            alt="Right Image"
            className="w-12 h-8 sm:w-16 sm:h-10 lg:w-20 lg:h-12 bg-transparent"
          />
        </motion.div>
      )}

      {/* Title */}
      <motion.h1
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="text-xl sm:text-2xl lg:text-3xl font-bold bg-transparent text-white"
      >
        {title}
      </motion.h1>

      {/* Subtitle */}
      {subtitle && (
        <motion.p
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-2 text-gray-300 text-sm sm:text-base bg-transparent px-2"
        >
          {subtitle}
        </motion.p>
      )}
    </>
  );
};

export default AuthHeader;
