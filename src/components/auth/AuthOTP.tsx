"use client";
import { motion } from "framer-motion";
import OTPInput from "react-otp-input";

interface AuthOTPProps {
  title: string;
  subtitle: string;
  value: string;
  onChange: (value: string) => void;
  numInputs?: number;
  delay?: number;
}

const AuthOTP = ({
  title,
  subtitle,
  value,
  onChange,
  numInputs = 6,
  delay = 0.9
}: AuthOTPProps) => {
  return (
    <>
      {/* OTP Title and Description */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay }}
        className="mt-4 sm:mt-6"
      >
        <p className="text-base sm:text-lg text-white font-bold bg-transparent">
          {title}
        </p>
        <p className="text-gray-300 text-center text-xs sm:text-sm bg-transparent mt-1 px-2">
          {subtitle}
        </p>
      </motion.div>

      {/* OTP Input */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: delay + 0.1 }}
        className="mt-3 sm:mt-4 flex gap-1 sm:gap-2 justify-center bg-transparent z-20 relative"
        dir="ltr"
      >
        <OTPInput
          inputType="tel"
          value={value}
          numInputs={numInputs}
          onChange={onChange}
          renderInput={(props) => (
            <input
              {...props}
              className="!w-[40px] !h-[44px] sm:!w-[50px] sm:!h-[54px] !text-white !font-bold !bg-[#141416]/80 !border !border-gray-700 !rounded-xl !mx-0.5 sm:!mx-1 focus:!border-blue-500 focus:!shadow-[0px_0px_15px_rgba(72,153,235,0.3)] !transition-all !duration-300 !outline-none !text-sm sm:!text-base"
            />
          )}
          containerStyle="flex justify-center items-center gap-1 sm:gap-2"
        />
      </motion.div>
    </>
  );
};

export default AuthOTP;
