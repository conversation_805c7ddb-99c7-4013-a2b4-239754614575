"use client";
import { motion } from "framer-motion";
import { ChangeEvent } from "react";

interface AuthSelectOption {
  value: string;
  label: string;
}

interface AuthSelectProps {
  name?: string;
  placeholder: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLSelectElement>) => void;
  options: AuthSelectOption[];
  delay?: number;
  animationDirection?: "left" | "right" | "up";
  className?: string;
}

const AuthSelect = ({
  name,
  placeholder,
  value,
  onChange,
  options,
  delay = 0.7,
  animationDirection = "left",
  className = ""
}: AuthSelectProps) => {
  const getAnimationProps = () => {
    switch (animationDirection) {
      case "left":
        return { initial: { x: -20, opacity: 0 }, animate: { x: 0, opacity: 1 } };
      case "right":
        return { initial: { x: 20, opacity: 0 }, animate: { x: 0, opacity: 1 } };
      default:
        return { initial: { y: 20, opacity: 0 }, animate: { y: 0, opacity: 1 } };
    }
  };

  return (
    <motion.div
      {...getAnimationProps()}
      transition={{ duration: 0.5, delay }}
      className="relative group"
    >
      <select
        name={name}
        value={value}
        onChange={onChange}
        className={`w-full p-2.5 sm:p-3 px-3 sm:px-4 border border-gray-700 rounded-xl bg-[#141416]/80 text-gray-300 h-10 sm:h-12 lg:h-[50px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right appearance-none text-sm sm:text-base ${className}`}
        style={{ 
          backgroundImage: "url('/images/arrow-down.png')", 
          backgroundRepeat: "no-repeat", 
          backgroundPosition: "left 12px center", 
          backgroundSize: "14px" 
        }}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
    </motion.div>
  );
};

export default AuthSelect;
