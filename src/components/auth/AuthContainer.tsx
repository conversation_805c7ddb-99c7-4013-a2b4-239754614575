"use client";
import { motion } from "framer-motion";
import { ReactNode } from "react";

interface AuthContainerProps {
  children: ReactNode;
  maxWidth?: "sm" | "md" | "lg";
  showBackButton?: boolean;
  onBackClick?: () => void;
  backButtonText?: string;
}

const AuthContainer = ({ 
  children, 
  maxWidth = "md",
  showBackButton = false,
  onBackClick,
  backButtonText = "بازگشت"
}: AuthContainerProps) => {
  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md", 
    lg: "max-w-lg"
  };

  return (
    <div className={`w-full ${maxWidthClasses[maxWidth]} mx-auto font-dmSans`}>
      {/* Decorative elements - hidden on small screens */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.15 }}
        transition={{ duration: 1.5 }}
        className="absolute top-10 sm:top-20 right-10 sm:right-20 w-32 h-32 sm:w-64 sm:h-64 bg-blue-500 rounded-full blur-[60px] sm:blur-[120px] hidden sm:block"
      />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        className="absolute bottom-10 sm:bottom-20 left-10 sm:left-20 w-36 h-36 sm:w-72 sm:h-72 bg-purple-500 rounded-full blur-[60px] sm:blur-[120px] hidden sm:block"
      />

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative bg-gradient-to-br from-[#23262F]/80 to-[#1C1E24]/80 p-4 sm:p-6 lg:p-8 border border-gray-800/50 rounded-2xl shadow-2xl w-full text-center backdrop-blur-xl z-10 overflow-hidden"
      >
        {/* Back button */}
        {showBackButton && onBackClick && (
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.7)" }}
            whileTap={{ scale: 0.95 }}
            onClick={onBackClick}
            className="absolute top-3 sm:top-4 left-3 sm:left-4 text-white bg-blue-600/60 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-300 z-20 backdrop-blur-sm border border-blue-500/30 shadow-md text-sm sm:text-base"
          >
            {backButtonText}
          </motion.button>
        )}

        {/* Top gradient line */}
        <div
          className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[2px] shadow-[0px_0px_15px_5px_rgba(72,153,235,0.5)]"
          style={{
            background:
              "linear-gradient(90deg,rgba(211, 211, 211, 0.1) 0%, rgba(72,153,235,1) 50%, rgba(211, 211, 211, 0.1) 100%)",
          }}
        />

        {/* Decorative grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

        {/* Decorative circles */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500 opacity-5 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-500 opacity-5 rounded-full -ml-16 -mb-16"></div>

        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>
      </motion.div>
    </div>
  );
};

export default AuthContainer;
