"use client";
import { motion } from "framer-motion";
import { ChangeEvent, ReactNode } from "react";

interface AuthInputProps {
  type?: "text" | "tel" | "email" | "password";
  name?: string;
  placeholder: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  icon?: ReactNode;
  maxLength?: number;
  autoFocus?: boolean;
  delay?: number;
  animationDirection?: "left" | "right" | "up";
  className?: string;
}

const AuthInput = ({
  type = "text",
  name,
  placeholder,
  value,
  onChange,
  icon,
  maxLength,
  autoFocus = false,
  delay = 0.6,
  animationDirection = "up",
  className = ""
}: AuthInputProps) => {
  const getAnimationProps = () => {
    switch (animationDirection) {
      case "left":
        return { initial: { x: -20, opacity: 0 }, animate: { x: 0, opacity: 1 } };
      case "right":
        return { initial: { x: 20, opacity: 0 }, animate: { x: 0, opacity: 1 } };
      default:
        return { initial: { y: 20, opacity: 0 }, animate: { y: 0, opacity: 1 } };
    }
  };

  return (
    <motion.div
      {...getAnimationProps()}
      transition={{ duration: 0.5, delay }}
      className={`relative ${animationDirection === "up" ? "mt-6 sm:mt-8 w-full" : ""} bg-transparent`}
    >
      <motion.div
        whileFocus={{ scale: 1.02 }}
        className="relative group"
      >
        <input
          type={type}
          name={name}
          autoFocus={autoFocus}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          maxLength={maxLength}
          className={`w-full p-2.5 sm:p-3 lg:p-4 ${icon ? "pl-10 sm:pl-12" : "pl-3 sm:pl-4"} border border-gray-700 rounded-xl bg-[#141416]/80 text-white h-10 sm:h-12 lg:h-[60px] outline-none focus:border-blue-500 focus:shadow-[0px_0px_15px_rgba(72,153,235,0.3)] transition-all duration-300 text-right text-sm sm:text-base ${className}`}
        />
        
        {/* Icon */}
        {icon && (
          <span className="absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 w-5 h-5 sm:w-6 sm:h-6 text-gray-400 bg-transparent transition-all duration-300 group-focus-within:text-blue-400">
            {icon}
          </span>
        )}

        {/* Subtle glow effect on focus */}
        <div className="absolute inset-0 rounded-xl opacity-0 group-focus-within:opacity-100 pointer-events-none transition-opacity duration-300 bg-blue-500/5"></div>
      </motion.div>
    </motion.div>
  );
};

export default AuthInput;
