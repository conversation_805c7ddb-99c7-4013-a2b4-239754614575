"use client";
import { motion } from "framer-motion";
import BTN from "@/components/form/BTN";

interface AuthFooterProps {
  buttonLabel: string;
  buttonType?: "button" | "submit" | "reset";
  onButtonClick?: () => void;
  showTimer?: boolean;
  timeLeft?: number;
  onResend?: () => void;
  isResending?: boolean;
  resendText?: string;
  error?: string;
  delay?: number;
}

const AuthFooter = ({
  buttonLabel,
  buttonType = "submit",
  onButtonClick,
  showTimer = false,
  timeLeft = 0,
  onResend,
  isResending = false,
  resendText = "هنوز کد را دریافت نکردید؟",
  error,
  delay = 1.1
}: AuthFooterProps) => {
  const formatTime = (seconds: number) => {
    if (showTimer && timeLeft > 60) {
      // For longer timers, show minutes:seconds
      return `${Math.floor(seconds / 60)}:${seconds % 60 < 10 ? "0" : ""}${seconds % 60}`;
    }
    // For shorter timers, show 00:seconds
    return `00:${seconds < 10 ? `0${seconds}` : seconds}`;
  };

  return (
    <>
      {/* Error Message */}
      {error && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-2 sm:mt-3 text-red-400 text-xs sm:text-sm bg-transparent px-2"
        >
          {error}
        </motion.p>
      )}

      {/* Submit Button */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay }}
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.97 }}
        className="mt-6 sm:mt-8 w-full"
      >
        <BTN
          label={buttonLabel}
          type={buttonType}
          className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 py-3 sm:py-4 rounded-xl text-white transition-all duration-300 shadow-lg hover:shadow-blue-500/30 font-medium text-base sm:text-lg relative overflow-hidden"
        />
        <div className="w-full h-1 bg-gradient-to-r from-blue-600/0 via-blue-400/50 to-blue-600/0 mt-1"></div>
      </motion.div>

      {/* Timer and Resend Section */}
      {showTimer && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: delay + 0.1 }}
          className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-300 flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-0 w-full bg-transparent"
        >
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-gray-300 bg-transparent px-2 sm:px-3 py-1 rounded-lg bg-gray-800/30 border border-gray-700/30 text-xs sm:text-sm"
          >
            {formatTime(timeLeft)}
          </motion.p>
          
          {onResend && (
            <motion.button
              whileHover={{ scale: 1.05, color: "#3b82f6" }}
              whileTap={{ scale: 0.95 }}
              disabled={timeLeft > 0 || isResending}
              onClick={onResend}
              className={`text-xs sm:text-sm transition-colors duration-300 text-center ${
                timeLeft > 0 || isResending
                  ? "text-gray-600 cursor-not-allowed"
                  : "text-gray-300 hover:text-blue-400"
              }`}
            >
              {isResending ? "در حال ارسال..." : resendText}
            </motion.button>
          )}
        </motion.div>
      )}
    </>
  );
};

export default AuthFooter;
