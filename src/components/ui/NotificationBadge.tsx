"use client";
import { motion, AnimatePresence } from "framer-motion";

interface NotificationBadgeProps {
  count?: number;
  show?: boolean;
  color?: "red" | "blue" | "green" | "orange";
  pulse?: boolean;
}

const NotificationBadge = ({ 
  count = 0, 
  show = false, 
  color = "red",
  pulse = true 
}: NotificationBadgeProps) => {
  const colorClasses = {
    red: "bg-gradient-to-br from-red-500 to-red-600 shadow-[0_0_10px_rgba(239,68,68,0.6)]",
    blue: "bg-gradient-to-br from-blue-500 to-blue-600 shadow-[0_0_10px_rgba(59,130,246,0.6)]",
    green: "bg-gradient-to-br from-green-500 to-green-600 shadow-[0_0_10px_rgba(34,197,94,0.6)]",
    orange: "bg-gradient-to-br from-orange-500 to-orange-600 shadow-[0_0_10px_rgba(251,146,60,0.6)]"
  };

  if (!show && count === 0) return null;

  return (
    <AnimatePresence>
      {(show || count > 0) && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0, opacity: 0 }}
          transition={{ type: "spring", damping: 15, stiffness: 300 }}
          className={`absolute -top-1 -right-1 min-w-[18px] h-[18px] ${colorClasses[color]} rounded-full flex items-center justify-center text-white text-xs font-bold border-2 border-gray-900`}
        >
          {pulse && (
            <motion.div
              className={`absolute inset-0 rounded-full ${colorClasses[color]} opacity-75`}
              animate={{ 
                scale: [1, 1.4, 1],
                opacity: [0.75, 0, 0.75]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          )}
          <span className="relative z-10">
            {count > 99 ? "99+" : count > 0 ? count : ""}
          </span>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NotificationBadge;
