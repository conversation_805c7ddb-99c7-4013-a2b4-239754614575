"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { FiX, FiCopy, FiExternalLink } from "react-icons/fi";
import { getDepositTransactionDetails } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";

interface DepositTransactionDetails {
  id: number;
  transaction_id: string;
  address: string;
  from_address: string;
  amount: {
    value: string;
    formatted: string;
    display: string;
  };
  received_amount: {
    value: string;
    formatted: string;
    display: string;
  };
  fees: {
    value: string;
    formatted: string;
    display: string;
  };
  btc_value?: {
    value: string;
    formatted: string;
    display: string;
  };
  usd_value?: {
    value: string;
    formatted: string;
    display: string;
  };
  status: {
    value: number;
    label: string;
    color: string;
  };
  confirmations: {
    current: number;
    required: number;
    percentage: number;
    is_confirmed: boolean;
  };
  coin: {
    id: number;
    name: string;
    coin_type: string;
    icon: string;
    decimal: number;
  };
  network: {
    id: number;
    name: string;
    slug: string;
    chain_id: string;
    block_confirmation: number;
  };
  receiver_wallet: {
    id: number;
    name: string;
    balance: string;
  };
  blockchain_info: {
    block_number: number;
    network_type: string;
    address_type: string;
  };
  admin_info: {
    is_admin_receive: boolean;
    updated_by: any;
    notification_status: string;
  };
  timestamps: {
    created_at: string;
    updated_at: string;
    created_at_persian: string;
    created_at_human: string;
  };
}

interface DepositTransactionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  transactionId: number;
}

const DepositTransactionDetailsModal: React.FC<DepositTransactionDetailsModalProps> = ({
  isOpen,
  onClose,
  transactionId,
}) => {
  const [details, setDetails] = useState<DepositTransactionDetails | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && transactionId) {
      fetchTransactionDetails();
    }
  }, [isOpen, transactionId]);

  const fetchTransactionDetails = async () => {
    setLoading(true);
    try {
      const result = await getDepositTransactionDetails(transactionId);
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت جزییات تراکنش");
      } else {
        setDetails(result.data);
      }
    } catch (error) {
      console.error("Error fetching transaction details:", error);
      toast.error("خطا در دریافت جزییات تراکنش");
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("کپی شد");
  };

  const getStatusColor = (status: { value: number; color: string }) => {
    switch (status.value) {
      case 1:
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 0:
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 3:
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getStatusDotColor = (status: { value: number; color: string }) => {
    switch (status.value) {
      case 1:
        return 'bg-green-400';
      case 0:
        return 'bg-yellow-400';
      case 3:
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] rounded-2xl border border-[#353945]/30 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-[#353945]/30">
          <h2 className="text-xl font-bold text-[#FCFCFD]">جزییات تراکنش واریز</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
          >
            <FiX className="w-5 h-5 text-[#B1B5C3]" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="space-y-6 animate-pulse">
              {/* Loading skeleton */}
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gray-600/30 rounded-full"></div>
                <div className="space-y-2">
                  <div className="h-6 bg-gray-600/30 rounded w-32"></div>
                  <div className="h-4 bg-gray-600/20 rounded w-24"></div>
                </div>
              </div>
              {Array(8).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-600/20 rounded w-24"></div>
                  <div className="h-6 bg-gray-600/30 rounded w-full"></div>
                </div>
              ))}
            </div>
          ) : details ? (
            <div className="space-y-6">
              {/* Coin Info */}
              <div className="flex items-center gap-4 p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
                <div className="relative">
                  <Image
                    className="w-16 h-16 rounded-full border-2 border-[#353945]"
                    src={`https://api.exchangim.com/storage/${details.coin.icon}`}
                    height={64}
                    width={64}
                    alt={details.coin.coin_type}
                    onError={(e) => {
                      e.currentTarget.src = '/images/default-coin.svg';
                    }}
                  />
                </div>
                <div className="flex-1">
                  <div className="text-2xl font-bold text-[#FCFCFD]">
                    {details.coin.coin_type}
                  </div>
                  <div className="text-lg text-[#B1B5C3]">
                    {details.coin.name}
                  </div>
                  <div className="text-sm text-[#B1B5C3]">
                    {details.network.name}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-[#FCFCFD]">
                    {details.amount.display}
                  </div>
                  {details.usd_value && (
                    <div className="text-lg text-[#B1B5C3]">
                      {details.usd_value.display}
                    </div>
                  )}
                </div>
              </div>

              {/* Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm text-[#B1B5C3]">وضعیت تراکنش</label>
                  <div className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium border ${getStatusColor(details.status)}`}>
                    <div className={`w-2 h-2 rounded-full mr-2 ${getStatusDotColor(details.status)}`}></div>
                    {details.status.label}
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm text-[#B1B5C3]">تأیید شبکه</label>
                  <div className="text-[#FCFCFD] font-medium">
                    {details.confirmations.current.toLocaleString()} / {details.confirmations.required.toLocaleString()}
                    <span className="text-sm text-[#B1B5C3] mr-2">
                      ({details.confirmations.percentage.toFixed(2)}%)
                    </span>
                  </div>
                </div>
              </div>

              {/* Transaction Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#FCFCFD] border-b border-[#353945]/30 pb-2">
                  اطلاعات تراکنش
                </h3>
                
                {/* Transaction ID */}
                <div className="space-y-2">
                  <label className="text-sm text-[#B1B5C3]">شناسه تراکنش</label>
                  <div className="flex items-center gap-2 p-3 bg-[#23262F] rounded-lg border border-[#353945]/30">
                    <span className="text-sm text-[#FCFCFD] font-mono flex-1 break-all">
                      {details.transaction_id}
                    </span>
                    <button
                      onClick={() => copyToClipboard(details.transaction_id)}
                      className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
                      title="کپی شناسه تراکنش"
                    >
                      <FiCopy className="w-4 h-4 text-[#B1B5C3]" />
                    </button>
                  </div>
                </div>

                {/* Addresses */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm text-[#B1B5C3]">آدرس مقصد</label>
                    <div className="flex items-center gap-2 p-3 bg-[#23262F] rounded-lg border border-[#353945]/30">
                      <span className="text-sm text-[#FCFCFD] font-mono flex-1 break-all">
                        {details.address}
                      </span>
                      <button
                        onClick={() => copyToClipboard(details.address)}
                        className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
                        title="کپی آدرس"
                      >
                        <FiCopy className="w-4 h-4 text-[#B1B5C3]" />
                      </button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm text-[#B1B5C3]">آدرس مبدأ</label>
                    <div className="flex items-center gap-2 p-3 bg-[#23262F] rounded-lg border border-[#353945]/30">
                      <span className="text-sm text-[#FCFCFD] font-mono flex-1 break-all">
                        {details.from_address}
                      </span>
                      <button
                        onClick={() => copyToClipboard(details.from_address)}
                        className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
                        title="کپی آدرس"
                      >
                        <FiCopy className="w-4 h-4 text-[#B1B5C3]" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Amount Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-[#FCFCFD] border-b border-[#353945]/30 pb-2">
                    جزییات مبلغ
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">مبلغ ارسالی</label>
                      <div className="text-[#FCFCFD] font-semibold text-lg">
                        {details.amount.display}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">مبلغ دریافتی</label>
                      <div className="text-[#FCFCFD] font-semibold text-lg">
                        {details.received_amount.display}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">کارمزد</label>
                      <div className="text-[#FCFCFD] font-semibold text-lg">
                        {details.fees.display}
                      </div>
                    </div>
                  </div>
                  {(details.btc_value || details.usd_value) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-[#353945]/30">
                      {details.btc_value && (
                        <div className="space-y-2">
                          <label className="text-sm text-[#B1B5C3]">ارزش بیت کوین</label>
                          <div className="text-[#FCFCFD] font-semibold">
                            {details.btc_value.display}
                          </div>
                        </div>
                      )}
                      {details.usd_value && (
                        <div className="space-y-2">
                          <label className="text-sm text-[#B1B5C3]">ارزش دلار</label>
                          <div className="text-[#FCFCFD] font-semibold">
                            {details.usd_value.display}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Blockchain Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-[#FCFCFD] border-b border-[#353945]/30 pb-2">
                    اطلاعات بلاک چین
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">شماره بلاک</label>
                      <div className="text-[#FCFCFD] font-semibold">
                        {details.blockchain_info.block_number.toLocaleString()}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">نوع شبکه</label>
                      <div className="text-[#FCFCFD] font-semibold">
                        {details.blockchain_info.network_type}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Wallet Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-[#FCFCFD] border-b border-[#353945]/30 pb-2">
                    اطلاعات کیف پول
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">نام کیف پول</label>
                      <div className="text-[#FCFCFD] font-semibold">
                        {details.receiver_wallet.name}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">موجودی کیف پول</label>
                      <div className="text-[#FCFCFD] font-semibold">
                        {parseFloat(details.receiver_wallet.balance).toFixed(8)} {details.coin.coin_type}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-[#FCFCFD] border-b border-[#353945]/30 pb-2">
                    زمان‌بندی
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">تاریخ ایجاد</label>
                      <div className="text-[#FCFCFD] font-semibold">
                        {details.timestamps.created_at_persian}
                      </div>
                      <div className="text-sm text-[#B1B5C3]">
                        {details.timestamps.created_at_human}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm text-[#B1B5C3]">آخرین بروزرسانی</label>
                      <div className="text-[#FCFCFD] font-semibold">
                        {new Date(details.timestamps.updated_at).toLocaleDateString('fa-IR')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Admin Info */}
                {details.admin_info.notification_status && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#FCFCFD] border-b border-[#353945]/30 pb-2">
                      اطلاعات سیستم
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm text-[#B1B5C3]">وضعیت اطلاع‌رسانی</label>
                        <div className="text-[#FCFCFD] font-semibold">
                          {details.admin_info.notification_status === 'sent' ? 'ارسال شده' : details.admin_info.notification_status}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm text-[#B1B5C3]">دریافت توسط ادمین</label>
                        <div className="text-[#FCFCFD] font-semibold">
                          {details.admin_info.is_admin_receive ? 'بله' : 'خیر'}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-[#B1B5C3]">خطا در بارگذاری جزییات تراکنش</div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-[#353945]/30">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-[#23262F] hover:bg-[#2A2D38] text-[#FCFCFD] rounded-lg border border-[#353945] transition-colors"
          >
            بستن
          </button>
        </div>
      </div>
    </div>
  );
};

export default DepositTransactionDetailsModal;
