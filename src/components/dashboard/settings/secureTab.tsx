"use client";

import Image from "next/image";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { getLoginHistory, get2FAStatus } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

interface SecurityOptionProps {
  iconSrc: string;
  title: string;
  description: string;
  actionText: string;
  onClick?: () => void;
  additionalActions?: { text: string; className: string; onClick?: () => void }[];
}

interface LoginHistory {
  id: number;
  ip: string;
  device: string;
  browser: string;
  platform: string;
  is_mobile: boolean;
  created_at: string;
}

interface TwoFactorStatus {
  enabled: boolean;
  has_secret: boolean;
}

interface TwoFactorSecret {
  secret: string;
  qr_code_url: string;
}

const SecurityOption: React.FC<SecurityOptionProps> = ({
  iconSrc,
  title,
  description,
  actionText,
  onClick,
  additionalActions,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="flex justify-between items-center p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 bg-gradient-to-br from-[#23262F] to-[#1C1E24] relative overflow-hidden group"
      whileHover={{ y: -2, boxShadow: "0 8px 16px rgba(0, 0, 0, 0.2)" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Decorative background elements */}
      <div className="absolute top-0 right-0 w-24 h-24 bg-blue-500 opacity-5 rounded-full -mr-10 -mt-10 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-green-500 opacity-5 rounded-full -ml-8 -mb-8 group-hover:opacity-10 transition-opacity"></div>

      <div className="flex items-center text-right gap-3 z-10">
        <motion.div
          className="w-10 h-10 rounded-full bg-gradient-to-br from-[#353945] to-[#23262F] flex items-center justify-center shadow-md"
          animate={isHovered ? { scale: 1.1, rotate: 5 } : { scale: 1, rotate: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Image className="w-5 h-5" src={iconSrc} height={20} width={20} alt="avatar" />
        </motion.div>
        <div className="mr-1">
          <p className="text-[#FCFCFD] font-medium">{title}</p>
          <p className="text-[#B1B5C3] text-sm">{description}</p>
        </div>
      </div>
      <div className="flex items-center gap-4 z-10">
        <motion.span
          className="bg-gradient-to-r from-blue-600/80 to-blue-800/80 hover:from-blue-500/80 hover:to-blue-700/80 rounded-md px-3 py-1.5 text-white font-medium cursor-pointer shadow-md"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onClick}
        >
          {actionText}
        </motion.span>
        {additionalActions && additionalActions.map((action, index) => (
          <motion.span
            key={index}
            className={`rounded-md px-3 py-1.5 cursor-pointer shadow-md ${action.className}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={action.onClick}
          >
            {action.text}
          </motion.span>
        ))}
      </div>
    </motion.div>
  );
};

const SecureTab: React.FC = () => {
  const router = useRouter();
  const [loginHistory, setLoginHistory] = useState<LoginHistory[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);

  // 2FA states
  const [twoFactorStatus, setTwoFactorStatus] = useState<TwoFactorStatus | null>(null);

  useEffect(() => {
    fetchLoginHistory();
    fetchTwoFactorStatus();
  }, [currentPage]);

  const fetchLoginHistory = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await getLoginHistory(currentPage);
      if (result.isError) {
        setError(result.message || "خطا در دریافت تاریخچه ورود");
        toast.error(result.message || "خطا در دریافت تاریخچه ورود");
      } else {
        setLoginHistory(result.data.logins || []);
        setTotalPages(result.data.pagination?.last_page || 1);
      }
    } catch (error) {
      setError("خطا در دریافت تاریخچه ورود");
      toast.error("خطا در دریافت تاریخچه ورود");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTwoFactorStatus = async () => {
    try {
      const result = await get2FAStatus();
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت وضعیت احراز هویت دو مرحله‌ای");
      } else {
        setTwoFactorStatus(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت وضعیت احراز هویت دو مرحله‌ای");
    }
  };

  const handleSetup2FA = () => {
    router.push("/dashboard/settings/2fa/setup");
  };

  const handleDisable2FA = () => {
    router.push("/dashboard/settings/2fa/disable");
  };

  const handleReset2FA = () => {
    router.push("/dashboard/settings/2fa/reset");
  };

  const handleTutorial2FA = () => {
    router.push("/dashboard/settings/2fa/tutorial");
  };

  // Format date to Jalali (Persian) format
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      // Format: YYYY/MM/DD HH:MM
      return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    } catch (e) {
      return dateString;
    }
  };



  return (
    <div className="relative">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500 opacity-5 rounded-full -mr-32 -mt-32"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-green-500 opacity-5 rounded-full -ml-32 -mb-32"></div>
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-10 pointer-events-none"></div>

      {/* Desktop View */}
      <div className="hidden md:block landscape:block">
        <motion.div
          className="space-y-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="p-6 space-y-6 rounded-2xl bg-gradient-to-br from-[#18191D] to-[#1C1E24] border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 shadow-lg relative overflow-hidden"
            whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
          >
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
            <motion.h2
              className="text-2xl text-[#FCFCFD] font-medium text-right mb-6 border-r-4 border-blue-500 pr-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              تنظیمات رمز عبور
            </motion.h2>
            <SecurityOption
              iconSrc="/images/lock.svg"
              title="تغییر رمز عبور"
              description="رمز عبور خود را به صورت دوره ای تغییر دهید"
              actionText="تغییر دهید"
            />
            <SecurityOption
              iconSrc="/images/mobile.svg"
              title="رمز ورود دو مرحله ای (پیامک)"
              description="برای هربار ورود به حساب کابری نیاز به تایید کد امنیتی از طریق پیامک دارید"
              actionText="تغییر دهید"
            />
            <SecurityOption
              iconSrc="/images/lock2.svg"
              title="رمز ورود دو مرحله ای (ایمیل)"
              description="برای هر بار ورود به حساب کاربری نیاز به تایید کد امنیتی از طریق ایمیل دارید"
              actionText="تغییر دهید"
            />
          </motion.div>

          <motion.div
            className="p-6 space-y-6 rounded-2xl bg-gradient-to-br from-[#18191D] to-[#1C1E24] border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 shadow-lg relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
          >
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
            <h2 className="text-2xl text-[#FCFCFD] font-medium text-right border-r-4 border-purple-500 pr-3">روش های ورود امن</h2>
{twoFactorStatus?.enabled ? (
              <SecurityOption
                iconSrc="/images/google-secure.svg"
                title="برنامه همگام ساز گوگل"
                description="احراز هویت دو مرحله‌ای با استفاده از Google Authenticator فعال است"
                actionText="بازنشانی"
                onClick={handleReset2FA}
                additionalActions={[
                  {
                    text: "غیرفعالسازی",
                    className: "text-[#E2464A] bg-[#E2464A]/10 hover:bg-[#E2464A]/20",
                    onClick: handleDisable2FA
                  }
                ]}
              />
            ) : (
              <SecurityOption
                iconSrc="/images/google-secure.svg"
                title="برنامه همگام ساز گوگل"
                description="حساب کاربری خود را به نرم افزار همگام سازی گوگل متصل کنید و امنیت را افزایش دهید"
                actionText="فعال‌سازی"
                onClick={handleSetup2FA}
                additionalActions={[
                  {
                    text: "آموزش فعالسازی",
                    className: "bg-[#23262F] hover:bg-[#2A2E38]",
                    onClick: handleTutorial2FA
                  }
                ]}
              />
            )}
          </motion.div>

          <motion.div
            className="p-6 space-y-6 rounded-2xl bg-gradient-to-br from-[#18191D] to-[#1C1E24] border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 shadow-lg relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
          >
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
            <h2 className="text-2xl text-[#FCFCFD] font-medium text-right border-r-4 border-green-500 pr-3">امنیت بیشتر</h2>
            <SecurityOption
              iconSrc="/images/monitor.svg"
              title="نشست های فعال"
              description="دستگاه های فعال در حساب کاربری خود را مشاهده کنید"
              actionText="مدیریت"
            />
          </motion.div>

          <motion.div
            className="p-6 space-y-6 rounded-2xl bg-gradient-to-br from-[#18191D] to-[#1C1E24] border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 shadow-lg relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
          >
            <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
            <h2 className="text-2xl text-[#FCFCFD] font-medium text-right border-r-4 border-orange-500 pr-3">دستگاه ها و IP های شناسایی شده</h2>
            <div className="flex justify-between bg-gradient-to-r from-[#23262F] to-[#1C1E24] p-3 rounded-lg border border-[#353945]/50 shadow-md">
              <span className="text-[#B1B5C3] font-medium">تاریخ ورود</span>
              <span className="text-[#B1B5C3] font-medium">IP وارد شده</span>
              <span className="text-[#B1B5C3] font-medium">دستگاه های شناسایی شده</span>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center p-10">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : error ? (
              <div className="flex flex-col justify-center items-center p-10 bg-[#1C1E24]/50 rounded-xl border border-[#353945]/20">
                <motion.div
                  whileHover={{ rotate: 10, scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image className="w-24 h-24 opacity-70" src="/images/glass.png" height={96} width={96} alt="avatar" />
                </motion.div>
                <span className="text-[#E2464A] mt-4 text-lg">{error}</span>
              </div>
            ) : loginHistory.length === 0 ? (
              <div className="flex flex-col justify-center items-center p-10 bg-[#1C1E24]/50 rounded-xl border border-[#353945]/20">
                <motion.div
                  whileHover={{ rotate: 10, scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image className="w-24 h-24 opacity-70" src="/images/glass.png" height={96} width={96} alt="avatar" />
                </motion.div>
                <span className="text-[#B1B5C3] mt-4 text-lg">گزینه ای پیدا نشد</span>
              </div>
            ) : (
              <div className="space-y-3">
                {loginHistory.map((login) => (
                  <div
                    key={login.id}
                    className="flex justify-between items-center p-3 bg-[#1C1E24]/50 rounded-lg border border-[#353945]/20 hover:border-[#353945]/50 transition-all duration-300"
                  >
                    <span className="text-[#FCFCFD]">{formatDate(login.created_at)}</span>
                    <span className="text-[#FCFCFD]">{login.ip}</span>
                    <span className="text-[#FCFCFD]">{login.device || 'ناشناس'} {login.is_mobile ? '(موبایل)' : ''}</span>
                  </div>
                ))}

                {totalPages > 1 && (
                  <div className="flex justify-center items-center gap-2 mt-4">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className={`px-3 py-1 rounded-md ${currentPage === 1 ? 'bg-[#353945]/30 text-[#777E90] cursor-not-allowed' : 'bg-[#353945] text-white hover:bg-[#4A4F5E] cursor-pointer'}`}
                    >
                      قبلی
                    </button>
                    <span className="text-[#B1B5C3]">صفحه {currentPage} از {totalPages}</span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className={`px-3 py-1 rounded-md ${currentPage === totalPages ? 'bg-[#353945]/30 text-[#777E90] cursor-not-allowed' : 'bg-[#353945] text-white hover:bg-[#4A4F5E] cursor-pointer'}`}
                    >
                      بعدی
                    </button>
                  </div>
                )}
              </div>
            )}
          </motion.div>
        </motion.div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden landscape:hidden space-y-6 flex flex-col items-center">
        <motion.div
          className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 rounded-xl w-[90vw] max-w-[550px] border border-[#353945]/30 shadow-lg relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
        >
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
          <motion.h2
            className="text-xl text-[#FCFCFD] font-medium mb-5 text-center border-b-2 border-blue-500/50 pb-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            تنظیمات رمز عبور
          </motion.h2>
          <div className="space-y-6">
            <MobileSecurityOption
              iconSrc="/images/lock.svg"
              title="تغییر رمز عبور"
              description="رمز عبور خود را به صورت دوره ای تغییر دهید"
              actionText="تغییر دهید"
            />
            <MobileSecurityOption
              iconSrc="/images/mobile.svg"
              title="رمز ورود دو مرحله ای (پیامک)"
              description="برای هربار ورود به حساب کابری نیاز به تایید کد امنیتی از طریق پیامک دارید"
              actionText="تغییر دهید"
            />
            <MobileSecurityOption
              iconSrc="/images/lock2.svg"
              title="رمز ورود دو مرحله ای (ایمیل)"
              description="برای هر بار ورود به حساب کاربری نیاز به تایید کد امنیتی از طریق ایمیل دارید"
              actionText="تغییر دهید"
            />
          </div>
        </motion.div>

        <motion.div
          className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 rounded-xl w-[90vw] max-w-[550px] border border-[#353945]/30 shadow-lg relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
        >
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
          <motion.h2
            className="text-xl text-[#FCFCFD] font-medium mb-5 text-center border-b-2 border-purple-500/50 pb-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            روش های ورود امن
          </motion.h2>
          <div className="space-y-6">
{twoFactorStatus?.enabled ? (
              <MobileSecurityOption
                iconSrc="/images/google-secure.svg"
                title="برنامه همگام ساز گوگل"
                description="احراز هویت دو مرحله‌ای با استفاده از Google Authenticator فعال است"
                actionText="بازنشانی"
                onClick={handleReset2FA}
                additionalActions={[
                  {
                    text: "غیرفعالسازی",
                    className: "text-[#E2464A] bg-[#E2464A]/10 hover:bg-[#E2464A]/20",
                    onClick: handleDisable2FA
                  }
                ]}
              />
            ) : (
              <MobileSecurityOption
                iconSrc="/images/google-secure.svg"
                title="برنامه همگام ساز گوگل"
                description="حساب کاربری خود را به نرم افزار همگام سازی گوگل متصل کنید و امنیت را افزایش دهید"
                actionText="فعال‌سازی"
                onClick={handleSetup2FA}
                additionalActions={[
                  {
                    text: "آموزش فعالسازی",
                    className: "bg-[#23262F] hover:bg-[#2A2E38]",
                    onClick: handleTutorial2FA
                  }
                ]}
              />
            )}
          </div>
        </motion.div>

        <motion.div
          className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 rounded-xl w-[90vw] max-w-[550px] border border-[#353945]/30 shadow-lg relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
        >
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
          <motion.h2
            className="text-xl text-[#FCFCFD] font-medium mb-5 text-center border-b-2 border-green-500/50 pb-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            امنیت بیشتر
          </motion.h2>
          <div className="space-y-6">
            <MobileSecurityOption
              iconSrc="/images/monitor.svg"
              title="نشست های فعال"
              description="دستگاه های فعال در حساب کاربری خود را مشاهده کنید"
              actionText="مدیریت"
            />
          </div>
        </motion.div>

        <motion.div
          className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-5 rounded-xl w-[90vw] max-w-[550px] border border-[#353945]/30 shadow-lg relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          whileHover={{ boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" }}
        >
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
          <motion.h2
            className="text-xl text-[#FCFCFD] font-medium mb-5 text-center border-b-2 border-orange-500/50 pb-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            دستگاه ها و IP های شناسایی شده
          </motion.h2>
          {isLoading ? (
            <div className="flex justify-center items-center p-6">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : error ? (
            <div className="flex flex-col justify-center items-center p-6 bg-[#1C1E24]/50 rounded-xl border border-[#353945]/20">
              <motion.div
                whileHover={{ rotate: 10, scale: 1.1 }}
                transition={{ duration: 0.3 }}
              >
                <Image className="w-20 h-20 opacity-70" src="/images/glass.png" height={80} width={80} alt="avatar" />
              </motion.div>
              <span className="text-[#E2464A] mt-3 text-base">{error}</span>
            </div>
          ) : loginHistory.length === 0 ? (
            <div className="flex flex-col justify-center items-center p-6 bg-[#1C1E24]/50 rounded-xl border border-[#353945]/20">
              <motion.div
                whileHover={{ rotate: 10, scale: 1.1 }}
                transition={{ duration: 0.3 }}
              >
                <Image className="w-20 h-20 opacity-70" src="/images/glass.png" height={80} width={80} alt="avatar" />
              </motion.div>
              <span className="text-[#B1B5C3] mt-3 text-base">گزینه ای پیدا نشد</span>
            </div>
          ) : (
            <div className="space-y-4 bg-[#1C1E24]/50 p-4 rounded-xl border border-[#353945]/20">
              {loginHistory.map((login) => (
                <div
                  key={login.id}
                  className="p-3 bg-[#23262F]/70 rounded-lg border border-[#353945]/20 hover:border-[#353945]/50 transition-all duration-300"
                >
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-[#B1B5C3] text-sm font-medium">تاریخ ورود:</span>
                    <span className="text-[#FCFCFD] text-sm">{formatDate(login.created_at)}</span>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-[#B1B5C3] text-sm font-medium">IP وارد شده:</span>
                    <span className="text-[#FCFCFD] text-sm">{login.ip}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#B1B5C3] text-sm font-medium">دستگاه:</span>
                    <span className="text-[#FCFCFD] text-sm">{login.device || 'ناشناس'} {login.is_mobile ? '(موبایل)' : ''}</span>
                  </div>
                </div>
              ))}

              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-2 mt-4">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded-md text-sm ${currentPage === 1 ? 'bg-[#353945]/30 text-[#777E90] cursor-not-allowed' : 'bg-[#353945] text-white hover:bg-[#4A4F5E] cursor-pointer'}`}
                  >
                    قبلی
                  </button>
                  <span className="text-[#B1B5C3] text-sm">صفحه {currentPage} از {totalPages}</span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded-md text-sm ${currentPage === totalPages ? 'bg-[#353945]/30 text-[#777E90] cursor-not-allowed' : 'bg-[#353945] text-white hover:bg-[#4A4F5E] cursor-pointer'}`}
                  >
                    بعدی
                  </button>
                </div>
              )}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

const MobileSecurityOption: React.FC<SecurityOptionProps> = ({
  iconSrc,
  title,
  description,
  actionText,
  onClick,
  additionalActions,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="flex flex-col items-center text-center p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/80 transition-all duration-300 bg-gradient-to-br from-[#23262F] to-[#1C1E24] relative overflow-hidden"
      whileHover={{ y: -2, boxShadow: "0 8px 16px rgba(0, 0, 0, 0.2)" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Decorative background elements */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500 opacity-5 rounded-full -mr-10 -mt-10 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-green-500 opacity-5 rounded-full -ml-8 -mb-8 group-hover:opacity-10 transition-opacity"></div>

      <motion.div
        className="w-12 h-12 rounded-full bg-gradient-to-br from-[#353945] to-[#23262F] flex items-center justify-center shadow-md mb-3"
        animate={isHovered ? { scale: 1.1, rotate: 5 } : { scale: 1, rotate: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Image className="w-6 h-6" src={iconSrc} height={24} width={24} alt="avatar" />
      </motion.div>
      <span className="text-[#FCFCFD] font-medium text-base mb-1">{title}</span>
      <p className="text-[#B1B5C3] text-sm mb-4">{description}</p>
      <motion.span
        className="bg-gradient-to-r from-blue-600/80 to-blue-800/80 hover:from-blue-500/80 hover:to-blue-700/80 rounded-md px-4 py-2 text-white font-medium cursor-pointer shadow-md"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onClick}
      >
        {actionText}
      </motion.span>
      {additionalActions && (
        <div className="flex justify-center gap-3 mt-3">
          {additionalActions.map((action, index) => (
            <motion.span
              key={index}
              className={`rounded-md px-3 py-1.5 text-sm cursor-pointer shadow-md ${action.className}`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={action.onClick}
            >
              {action.text}
            </motion.span>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default SecureTab;
