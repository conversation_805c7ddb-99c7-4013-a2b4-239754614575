"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useRef, useEffect } from "react";
import { RiMenu3Line } from "react-icons/ri";
import { getProfile, readNotif } from "@/requests/dashboardRequest";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import jalaali from "jalaali-js";
import { motion } from "framer-motion";
import { FaMoon, FaSun, FaLanguage, FaWallet, FaChartLine, FaBell, FaSearch, FaCreditCard, FaUser } from "react-icons/fa";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import { useUserProfileModal } from "@/contexts/UserProfileModalContext";

interface IUserProfile {
  national_id?: string;
  firstname?: string;
  lastname?: string;
  birth_date?: string;
  phone?: string;
  alerts?: { id: number; message: string; created_at: string; read: number }[];
  tomanBalance?: number;
}
const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const { isUserModalOpen, openUserModal, closeUserModal } = useUserProfileModal();
  const [isShowNotif, setIsShowNotif] = useState<boolean>(false);

  const [isThemeMenuOpen, setIsThemeMenuOpen] = useState<boolean>(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState<boolean>(false);
  const [isQuickMenuOpen, setIsQuickMenuOpen] = useState<boolean>(false);
  const [isSearchActive, setIsSearchActive] = useState<boolean>(false);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(true);
  const [currentLanguage, setCurrentLanguage] = useState<string>("fa");
  const [info, setInfo] = useState<IUserProfile>({});
  const [searchQuery, setSearchQuery] = useState<string>("");

  const modalRef = useRef<HTMLDivElement | null>(null);
  const notifModalRef = useRef<HTMLDivElement>(null);
  const themeMenuRef = useRef<HTMLDivElement>(null);
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const quickMenuRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close notification panel when clicking outside
      if (
        notifModalRef.current &&
        !notifModalRef.current.contains(event.target as Node)
      ) {
        setIsShowNotif(false);
      }

      // Close theme menu when clicking outside
      if (
        themeMenuRef.current &&
        !themeMenuRef.current.contains(event.target as Node)
      ) {
        setIsThemeMenuOpen(false);
      }

      // Close language menu when clicking outside
      if (
        languageMenuRef.current &&
        !languageMenuRef.current.contains(event.target as Node)
      ) {
        setIsLanguageMenuOpen(false);
      }

      // Close quick menu when clicking outside
      if (
        quickMenuRef.current &&
        !quickMenuRef.current.contains(event.target as Node)
      ) {
        setIsQuickMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  async function getProfileHandler() {
    const result = await getProfile();
    if (result.isError) {
      toast.error("خطایی رخ داد");
    } else {
      setInfo(result.data);
    }
  }

  useEffect(() => {
    getProfileHandler();
    function handleClickOutside(event: MouseEvent) {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        closeUserModal();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("userPhone");
    document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    toast.success("با موفقیت از حساب کاربری خارج شدید");
    closeUserModal();
    router.push("/");
  };

  const readNotifHandler = async (id: number) => {
    const result = await readNotif(id);
    if (result.status === "success") {
      toast.success("عملیات با موفقیت انجام شد");
      getProfileHandler();
    }
  };

  const handleNotificationClick = () => {
    // Check if it's mobile view
    if (window.innerWidth < 768) {
      // Navigate to messages page on mobile
      router.push('/dashboard/profile/messages');
    } else {
      setIsShowNotif(!isShowNotif);
    }
  };

  const toPersianNumber = (num: string): string => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num
      .toString()
      .replace(/[0-9]/g, (match) => persianDigits[parseInt(match)]);
  };

  const convertToJalali = (dateString: string): string => {
    const date = new Date(dateString);
    const jalaliDate = jalaali.toJalaali(date);
    return toPersianNumber(
      `${jalaliDate.jy}/${jalaliDate.jm}/${jalaliDate.jd}`
    );
  };

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed top-5 left-5 right-5 z-50 flex items-center justify-between backdrop-blur-sm bg-gradient-to-r from-[#141416]/95 via-[#1E1F25]/95 to-[#141416]/95 rounded-2xl h-20 px-4 lg:px-8 py-2 lg:flex-row-reverse border border-gray-800/50 shadow-[0_0_15px_rgba(0,0,0,0.5)] lg:right"
    >
      {/* Mobile Layout */}
      <div className="flex items-center justify-between w-full lg:hidden">
        {/* Left side - Logo */}
        <div className="flex-shrink-0">
          <motion.div
            initial={{ opacity: 0, scale: 0.8, x: -20 }}
            animate={{ opacity: 1, scale: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 300 }}
            whileHover={{ scale: 1.08, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="relative group cursor-pointer"
          >
            {/* Background glow */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 via-purple-500/20 to-blue-500/30 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-all duration-500 -z-10 scale-110"></div>

            {/* Logo container */}
            <div className="relative bg-gradient-to-r from-[#1A1D24]/80 to-[#23262F]/80 backdrop-blur-sm rounded-xl px-3 py-2 border border-gray-700/50 shadow-[0_4px_20px_rgba(0,0,0,0.3)] group-hover:border-blue-500/30 transition-all duration-300">
              <Image
                className="w-28 h-9 object-contain filter brightness-110 group-hover:brightness-125 transition-all duration-300"
                src="/images/main-logo.png"
                height={120}
                width={350}
                alt="Exchangim Logo"
                priority
              />

              {/* Shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-pulse rounded-xl transition-opacity duration-300"></div>
            </div>

            {/* Floating particles effect */}
            <motion.div
              className="absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100"
              animate={{
                y: [0, -8, 0],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: 0.5
              }}
            />
            <motion.div
              className="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-purple-400 rounded-full opacity-0 group-hover:opacity-100"
              animate={{
                y: [0, -6, 0],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                delay: 1
              }}
            />
          </motion.div>
        </div>

        {/* Center - Empty space for balance */}
        <div className="flex-1 flex justify-center">
          {/* Empty center space */}
        </div>

        {/* Right side - Action Icons */}
        <div className="flex-shrink-0">
          <div className="flex items-center gap-x-1.5 bg-gradient-to-r from-[#23262F]/60 to-[#1A1D24]/60 backdrop-blur-sm rounded-full p-1 border border-gray-700/30">
            {/* Notification Bell */}
            <motion.div
              whileHover={{ scale: 1.15, backgroundColor: "rgba(59, 130, 246, 0.1)" }}
              whileTap={{ scale: 0.9 }}
              onClick={handleNotificationClick}
              className="cursor-pointer p-2.5 rounded-full hover:bg-blue-500/10 transition-all duration-300 relative group"
            >
              <FaBell className="w-4 h-4 text-gray-300 group-hover:text-blue-400 transition-colors" />
              {info?.alerts?.length ? (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 15 }}
                  className="absolute bg-gradient-to-r from-red-500 to-red-600 w-4 h-4 flex justify-center items-center rounded-full text-xs font-bold -top-0.5 -right-0.5 border border-gray-800 shadow-[0_0_8px_rgba(239,68,68,0.6)]"
                >
                  <motion.span
                    animate={{ scale: [0.9, 1.1, 0.9] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="text-[10px]"
                  >
                    {info?.alerts?.length > 9 ? '9+' : info?.alerts?.length}
                  </motion.span>
                </motion.span>
              ) : null}
            </motion.div>

            {/* User Profile - Mobile also shows modal */}
            <motion.div
              whileHover={{ scale: 1.15, backgroundColor: "rgba(59, 130, 246, 0.1)" }}
              whileTap={{ scale: 0.9 }}
              onClick={() => isUserModalOpen ? closeUserModal() : openUserModal()}
              className="cursor-pointer p-2.5 rounded-full hover:bg-blue-500/10 transition-all duration-300 relative group lg:hidden"
            >
              <FaUser className="w-4 h-4 text-gray-300 group-hover:text-blue-400 transition-colors" />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex items-center justify-start gap-x-3 lg:gap-x-5 lg:flex-row-reverse w-full lg:w-auto">

        {/* Desktop User Profile Section */}
        <div className="relative">
        </div>

        {/* Desktop Action Icons Section */}
        <div className="flex items-center justify-center gap-x-3 lg:gap-x-5 lg:order-last">
          {/* Notification Bell */}
          <div className="relative">
            <motion.div
              whileHover={{ scale: 1.1, boxShadow: "0 0 15px rgba(59, 130, 246, 0.3)" }}
              whileTap={{ scale: 0.9 }}
              onClick={handleNotificationClick}
              className={`cursor-pointer p-2 rounded-full transition-all duration-300 ${
                isShowNotif
                  ? "bg-gradient-to-r from-blue-600 to-blue-500 shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                  : "bg-[#23262F]/80 hover:bg-[#2A2D38]"
              }`}
            >
              <FaBell className={`w-5 h-5 ${isShowNotif ? "text-white" : "text-gray-300 hover:text-white"} transition-colors`} />
              {info?.alerts?.length ? (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 15
                  }}
                  className="absolute bg-gradient-to-r from-red-500 to-red-600 w-5 h-5 flex justify-center items-center rounded-full text-xs font-bold -top-1 -right-1 border border-gray-800 shadow-[0_0_10px_rgba(239,68,68,0.5)]"
                >
                  <motion.span
                    animate={{ scale: [0.9, 1.1, 0.9] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    {info?.alerts?.length}
                  </motion.span>
                </motion.span>
              ) : null}
            </motion.div>

            {isShowNotif && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 25 }}
                ref={notifModalRef}
                className="hidden md:block absolute -left-50 top-12 md:-left-20 md:top-12 bg-gradient-to-br from-[#18191d] to-[#1C1E24] max-h-[400px] overflow-y-auto w-[280px] md:w-[350px] border border-gray-700/50 rounded-xl z-[60] shadow-[0_10px_40px_rgba(0,0,0,0.5)] backdrop-blur-sm"
              >
                {/* Decorative top gradient line */}
                <div
                  className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
                  style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
                />

                <div className="sticky top-0 bg-gradient-to-r from-[#23262F] to-[#1C1E24] p-3 border-b border-gray-700/50 flex justify-between items-center backdrop-blur-sm z-10">
                  <div className="flex items-center gap-2">
                    <FaBell className="text-blue-400 w-4 h-4" />
                    <h3 className="text-white font-medium">اعلان‌ها</h3>
                  </div>
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="flex items-center gap-1 bg-blue-500/10 px-2 py-1 rounded-full"
                  >
                    <span className="text-xs text-blue-400 font-medium">{info?.alerts?.length || 0}</span>
                    <span className="text-xs text-gray-400">اعلان</span>
                  </motion.div>
                </div>

                {info?.alerts?.length ? (
                  <div className="relative">
                    {/* Subtle pattern background */}
                    <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-10 pointer-events-none"></div>

                    {info.alerts.map((item, index) => (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05, duration: 0.3 }}
                        key={item.id}
                        className={`relative border-b border-gray-800/50 p-4 transition-all duration-300 ${
                          item.read === 0
                            ? "hover:bg-gradient-to-r hover:from-blue-600/10 hover:to-blue-500/5"
                            : "hover:bg-[#23262F]/30"
                        }`}
                        whileHover={{ y: -2 }}
                      >
                        {/* Unread indicator line */}
                        {item.read === 0 && (
                          <motion.div
                            className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-blue-600"
                            layoutId={`indicator-${item.id}`}
                            initial={{ height: 0 }}
                            animate={{ height: '100%' }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                          />
                        )}

                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-400 bg-[#23262F]/80 px-2 py-1 rounded-full">
                              {convertToJalali(item.created_at)}
                            </span>
                          </div>
                          {item.read === 0 && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, delay: index * 0.05 + 0.2 }}
                              className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full shadow-[0_0_10px_rgba(59,130,246,0.5)]"
                            />
                          )}
                        </div>

                        <div className="flex items-start gap-x-3">
                          <div className="flex-1">
                            <p className={`text-sm ${item.read === 0 ? 'text-white' : 'text-gray-400'} transition-colors duration-300`}>
                              {item.message}
                            </p>
                          </div>

                          {item.read === 0 && (
                            <motion.button
                              whileHover={{ scale: 1.05, boxShadow: "0 0 15px rgba(59, 130, 246, 0.5)" }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => readNotifHandler(item.id)}
                              type="button"
                              className="text-white bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 rounded-lg text-xs px-3 py-1.5 cursor-pointer shadow-lg transition-all duration-300 flex items-center gap-1"
                            >
                              <span>خواندن</span>
                              <motion.div
                                animate={{ x: [0, 5, 0] }}
                                transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                                className="w-2 h-2 bg-white rounded-full opacity-70"
                              />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="flex flex-col items-center justify-center py-12 text-center"
                  >
                    <div className="bg-[#23262F] p-4 rounded-full mb-4">
                      <FaBell className="w-8 h-8 text-gray-500" />
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">اعلانی وجود ندارد</h3>
                    <p className="text-gray-400 text-sm max-w-[250px]">
                      اعلان‌های مهم و به‌روزرسانی‌های سیستم در اینجا نمایش داده می‌شوند.
                    </p>
                  </motion.div>
                )}

                {(info?.alerts?.length || 0) > 5 && (
                  <div className="sticky bottom-0 bg-gradient-to-t from-[#18191d] to-transparent h-10 w-full pointer-events-none" />
                )}
              </motion.div>
            )}
          </div>


          {/* Quick Access Menu - Desktop */}
          <div className="relative hidden lg:block">
            <motion.div
              whileHover={{ scale: 1.1, boxShadow: "0 0 15px rgba(59, 130, 246, 0.3)" }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsQuickMenuOpen(!isQuickMenuOpen)}
              className={`cursor-pointer p-2 rounded-full transition-all duration-300 ${
                isQuickMenuOpen
                  ? "bg-gradient-to-r from-blue-600 to-blue-500 shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                  : "bg-[#23262F]/80 hover:bg-[#2A2D38]"
              }`}
            >
              <FaChartLine className={`w-5 h-5 ${isQuickMenuOpen ? "text-white" : "text-gray-300 hover:text-white"} transition-colors`} />
            </motion.div>

            {isQuickMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                ref={quickMenuRef}
                className="absolute -left-20 top-12 bg-[#23262F] w-56 border border-gray-700 rounded-lg z-[60] shadow-[0_5px_25px_rgba(0,0,0,0.3)] overflow-hidden"
              >
                <div className="p-2 border-b border-gray-700">
                  <h3 className="text-white font-medium text-sm">دسترسی سریع</h3>
                </div>
                <div className="p-2 grid grid-cols-2 gap-2">
                  <Link href="/dashboard/deposit">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaWallet size={18} />
                      <span className="text-xs">واریز</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/withdraw">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaWallet size={18} />
                      <span className="text-xs">برداشت</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/card-managment">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaCreditCard size={18} />
                      <span className="text-xs">کارت‌های بانکی</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/price">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaChartLine size={18} />
                      <span className="text-xs">قیمت‌ها</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/profile">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaUser size={18} />
                      <span className="text-xs">پروفایل</span>
                    </motion.div>
                  </Link>
                </div>
              </motion.div>
            )}
          </div>

          {/* User Profile - Desktop */}
          <div className="relative hidden lg:block">
            <motion.div
              whileHover={{ scale: 1.1, boxShadow: "0 0 15px rgba(59, 130, 246, 0.3)" }}
              whileTap={{ scale: 0.9 }}
              onClick={() => isUserModalOpen ? closeUserModal() : openUserModal()}
              className={`cursor-pointer p-2 rounded-full transition-all duration-300 ${
                isUserModalOpen
                  ? "bg-gradient-to-r from-blue-600 to-blue-500 shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                  : "bg-[#23262F]/80 hover:bg-[#2A2D38]"
              }`}
            >
              <FaUser className={`w-5 h-5 ${isUserModalOpen ? "text-white" : "text-gray-300 hover:text-white"} transition-colors`} />
            </motion.div>
          </div>

        </div>
      </div>

      {/* Desktop Navigation */}
      <nav className="hidden lg:flex items-center gap-x-4 ml-auto lg:flex-row-reverse">
        <Link href="/support" className="text-lg font-light">
          تماس با ما
        </Link>
        <Link href="/blog" className="text-lg font-light">
          وبلاگ
        </Link>
        <Link href="/dashboard/price" className="text-lg font-light">
          قیمت لحظه ای
        </Link>
        <Link href="/" className="text-lg font-light">
          خرید و فروش رمز ارز
        </Link>
        <Image
          className="w-40 h-15 mx-auto"
          src="/images/main-logo.png"
          height={1000}
          width={1000}
          alt="logo"
        />
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-[55] lg:hidden h-screen"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}



    </motion.header>
  );
};

export default Header;
