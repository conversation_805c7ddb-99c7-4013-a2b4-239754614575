"use client";
import { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaHome,
  FaWallet,
  FaHistory,
  FaUser,
  FaPlus,
  FaChevronUp,
  FaChevronDown
} from "react-icons/fa";
import NotificationBadge from "@/components/ui/NotificationBadge";

interface BottomBarItem {
  id: string;
  label: string;
  icon: string;
  href?: string;
  hasSubmenu?: boolean;
  notificationCount?: number;
  hasNotification?: boolean;
  submenu?: {
    label: string;
    href: string;
    icon?: string;
  }[];
}

const BottomBar = () => {
  const pathname = usePathname();
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);

  // Haptic feedback function
  const triggerHaptic = () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate(50); // Light vibration
    }
  };

  const bottomBarItems: BottomBarItem[] = [
    {
      id: "home",
      label: "خانه",
      icon: "/images/home.svg",
      href: "/dashboard"
    },
    {
      id: "wallet",
      label: "کیف پول",
      icon: "/images/asset.svg",
      hasSubmenu: true,
      notificationCount: 2, // Example notification
      submenu: [
        { label: "واریز", href: "/dashboard/deposit", icon: "/images/deposit.png" },
        { label: "برداشت", href: "/dashboard/withdraw", icon: "/images/send.svg" },
        { label: "کارت‌ها", href: "/dashboard/card-managment", icon: "/images/credit-card.svg" }
      ]
    },
    {
      id: "history",
      label: "سوابق",
      icon: "/images/history.svg",
      hasSubmenu: true,
      submenu: [
        { label: "واریز", href: "/dashboard/history/deposit" },
        { label: "برداشت", href: "/dashboard/history/withdraw" },
        { label: "سفارش", href: "/dashboard/history/order" }
      ]
    },
    {
      id: "auth",
      label: "احراز",
      icon: "/images/auth.svg",
      href: "/dashboard/authenticate",
      hasNotification: false // Example notification dot
    },
    {
      id: "profile",
      label: "پروفایل",
      icon: "/images/setting.svg",
      hasSubmenu: true,
      notificationCount: 5, // Example notification
      submenu: [
        { label: "پیام‌ها", href: "/dashboard/profile/messages", icon: "/images/mail.svg" },
        { label: "تنظیمات", href: "/dashboard/settings", icon: "/images/setting.svg" },
        { label: "تیکت", href: "/dashboard/tickets", icon: "/images/mail.svg" },
        { label: "کسب درآمد", href: "/dashboard/referral", icon: "/images/star.png" }
      ]
    }
  ];

  const isItemActive = (item: BottomBarItem) => {
    if (item.href) {
      return item.href === "/dashboard" 
        ? pathname === "/dashboard"
        : pathname.startsWith(item.href);
    }
    
    if (item.submenu) {
      return item.submenu.some(subItem => pathname.startsWith(subItem.href));
    }
    
    return false;
  };

  const handleItemClick = (item: BottomBarItem) => {
    triggerHaptic(); // Add haptic feedback
    if (item.hasSubmenu) {
      setActiveSubmenu(activeSubmenu === item.id ? null : item.id);
    } else {
      setActiveSubmenu(null);
    }
  };

  const handleSubmenuClick = () => {
    triggerHaptic(); // Add haptic feedback
    setActiveSubmenu(null);
  };

  return (
    <>
      {/* Backdrop for submenu */}
      <AnimatePresence>
        {activeSubmenu && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setActiveSubmenu(null)}
          />
        )}
      </AnimatePresence>

      {/* Submenu Panel */}
      <AnimatePresence>
        {activeSubmenu && (
          <motion.div
            initial={{ y: "100%", opacity: 0, scale: 0.9 }}
            animate={{ y: 0, opacity: 1, scale: 1 }}
            exit={{ y: "100%", opacity: 0, scale: 0.9 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed bottom-24 left-4 right-4 z-50 lg:hidden"
          >
            {/* Glassmorphism Background */}
            <div className="relative bg-gradient-to-br from-[#0F1116]/95 via-[#18191D]/90 to-[#23262F]/95 rounded-3xl shadow-[0_20px_60px_rgba(0,0,0,0.8),0_0_40px_rgba(59,130,246,0.1)] border border-gray-700/30 backdrop-blur-2xl overflow-hidden">
              {/* Top Glow */}
              <div className="absolute top-0 left-1/2 -translate-x-1/2 w-32 h-[2px] bg-gradient-to-r from-transparent via-blue-500 to-transparent shadow-[0_0_20px_rgba(59,130,246,0.8)]" />

              {/* Floating Particles */}
              <div className="absolute top-4 left-6 w-1 h-1 bg-blue-400 rounded-full animate-pulse opacity-60" />
              <div className="absolute top-6 right-8 w-1 h-1 bg-purple-400 rounded-full animate-pulse opacity-40" style={{ animationDelay: '1s' }} />
              <div className="absolute bottom-4 left-1/3 w-0.5 h-0.5 bg-cyan-400 rounded-full animate-pulse opacity-50" style={{ animationDelay: '2s' }} />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-6">
                  <motion.h3
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    className="text-white font-semibold text-lg bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
                  >
                    {bottomBarItems.find(item => item.id === activeSubmenu)?.label}
                  </motion.h3>
                  <motion.button
                    initial={{ rotate: 180, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    whileHover={{ scale: 1.1, rotate: 180 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setActiveSubmenu(null)}
                    className="p-2 rounded-full bg-gradient-to-br from-gray-700/50 to-gray-800/50 text-gray-300 hover:text-white transition-all duration-300 border border-gray-600/30 hover:border-gray-500/50"
                  >
                    <FaChevronDown size={14} />
                  </motion.button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {bottomBarItems
                    .find(item => item.id === activeSubmenu)
                    ?.submenu?.map((subItem, index) => (
                      <Link
                        key={subItem.href}
                        href={subItem.href}
                        onClick={handleSubmenuClick}
                        className="group"
                      >
                        <motion.div
                          initial={{ opacity: 0, y: 30, scale: 0.8 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          transition={{
                            delay: index * 0.1,
                            type: "spring",
                            damping: 20,
                            stiffness: 300
                          }}
                          whileHover={{
                            scale: 1.05,
                            y: -5,
                            boxShadow: pathname.startsWith(subItem.href)
                              ? "0 10px 30px rgba(59,130,246,0.4)"
                              : "0 10px 25px rgba(255,255,255,0.1)"
                          }}
                          whileTap={{ scale: 0.95 }}
                          className={`relative flex flex-col items-center p-4 rounded-2xl transition-all duration-500 overflow-hidden ${
                            pathname.startsWith(subItem.href)
                              ? "bg-gradient-to-br from-blue-500/30 to-purple-500/20 border border-blue-400/40 shadow-[0_0_20px_rgba(59,130,246,0.3)]"
                              : "bg-gradient-to-br from-gray-800/40 to-gray-900/40 hover:from-gray-700/50 hover:to-gray-800/50 border border-gray-600/20 hover:border-gray-500/40"
                          }`}
                        >
                          {/* Background Glow */}
                          {pathname.startsWith(subItem.href) && (
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/10 rounded-2xl"
                              animate={{
                                opacity: [0.3, 0.6, 0.3],
                                scale: [1, 1.02, 1]
                              }}
                              transition={{ duration: 2, repeat: Infinity }}
                            />
                          )}

                          {subItem.icon && (
                            <motion.div
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.6 }}
                              className="relative z-10 mb-3"
                            >
                              <Image
                                src={subItem.icon}
                                alt={subItem.label}
                                width={24}
                                height={24}
                                className={`transition-all duration-300 ${
                                  pathname.startsWith(subItem.href)
                                    ? "brightness-125 drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]"
                                    : "opacity-80 group-hover:opacity-100 group-hover:brightness-110"
                                }`}
                              />
                            </motion.div>
                          )}

                          <span className={`relative z-10 text-sm font-medium transition-all duration-300 text-center ${
                            pathname.startsWith(subItem.href)
                              ? "text-blue-400 drop-shadow-[0_0_4px_rgba(59,130,246,0.8)]"
                              : "text-gray-300 group-hover:text-white"
                          }`}>
                            {subItem.label}
                          </span>

                          {/* Active Indicator */}
                          {pathname.startsWith(subItem.href) && (
                            <motion.div
                              layoutId="submenuActiveIndicator"
                              className="absolute top-2 right-2 w-2 h-2 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full shadow-[0_0_8px_rgba(59,130,246,0.8)]"
                              transition={{ type: "spring", damping: 25, stiffness: 300 }}
                            >
                              <div className="absolute inset-0.5 bg-white rounded-full animate-pulse" />
                            </motion.div>
                          )}
                        </motion.div>
                      </Link>
                    ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bottom Bar */}
      <motion.div
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
        className="fixed bottom-0 left-0 right-0 z-30 lg:hidden"
      >
        {/* Glassmorphism Background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-t from-[#0F1116]/95 via-[#18191D]/90 to-transparent backdrop-blur-2xl"
          animate={{
            background: [
              "linear-gradient(to top, rgba(15,17,22,0.95) 0%, rgba(24,25,29,0.90) 50%, transparent 100%)",
              "linear-gradient(to top, rgba(15,17,22,0.98) 0%, rgba(24,25,29,0.92) 50%, transparent 100%)",
              "linear-gradient(to top, rgba(15,17,22,0.95) 0%, rgba(24,25,29,0.90) 50%, transparent 100%)"
            ]
          }}
          transition={{ duration: 4, repeat: Infinity }}
        />

        {/* Top Glow Line */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-16 h-[2px] bg-gradient-to-r from-transparent via-blue-500 to-transparent shadow-[0_0_20px_rgba(59,130,246,0.8)]" />

        {/* Floating Orbs */}
        <motion.div
          className="absolute top-2 left-8 w-1 h-1 bg-blue-400 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.6, 1, 0.6],
            x: [0, 10, 0]
          }}
          transition={{ duration: 3, repeat: Infinity }}
        />
        <motion.div
          className="absolute top-4 right-12 w-1 h-1 bg-purple-400 rounded-full"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.8, 0.4],
            y: [0, -5, 0]
          }}
          transition={{ duration: 2.5, repeat: Infinity, delay: 1 }}
        />
        <motion.div
          className="absolute top-3 left-1/3 w-0.5 h-0.5 bg-cyan-400 rounded-full"
          animate={{
            scale: [1, 2, 1],
            opacity: [0.5, 1, 0.5],
            rotate: [0, 180, 360]
          }}
          transition={{ duration: 4, repeat: Infinity, delay: 2 }}
        />
        <div className="relative flex items-center justify-around px-4 py-4 safe-area-bottom">
          {bottomBarItems.map((item, index) => {
            const isActive = isItemActive(item);
            const hasActiveSubmenu = activeSubmenu === item.id;

            return (
              <motion.div
                key={item.id}
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{
                  delay: index * 0.1,
                  type: "spring",
                  damping: 20,
                  stiffness: 300
                }}
                whileTap={{ scale: 0.9 }}
                className="relative flex-1 flex justify-center"
              >
                {item.href ? (
                  <Link
                    href={item.href}
                    onClick={() => handleItemClick(item)}
                    className="flex flex-col items-center group"
                  >
                    <motion.div
                      className={`relative p-3 rounded-2xl transition-all duration-500 ${
                        isActive
                          ? "bg-gradient-to-br from-blue-500/30 to-purple-500/20 shadow-[0_0_25px_rgba(59,130,246,0.4),0_0_50px_rgba(147,51,234,0.2)] border border-blue-400/30"
                          : "bg-gray-800/20 hover:bg-gray-700/30 border border-transparent hover:border-gray-600/30"
                      }`}
                      whileHover={{
                        scale: 1.15,
                        rotateY: isActive ? 0 : 15,
                        rotateX: 5,
                        boxShadow: isActive
                          ? "0 0 40px rgba(59,130,246,0.8), 0 0 80px rgba(147,51,234,0.4), 0 10px 30px rgba(0,0,0,0.3)"
                          : "0 0 25px rgba(255,255,255,0.2), 0 5px 20px rgba(0,0,0,0.2)"
                      }}
                      whileTap={{
                        scale: 0.85,
                        rotateZ: isActive ? 0 : 5,
                        transition: { duration: 0.1 }
                      }}
                    >
                      {/* Animated Background Gradient */}
                      {isActive && (
                        <motion.div
                          className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/10"
                          animate={{
                            background: [
                              "linear-gradient(135deg, rgba(59,130,246,0.2) 0%, rgba(147,51,234,0.1) 100%)",
                              "linear-gradient(135deg, rgba(147,51,234,0.2) 0%, rgba(59,130,246,0.1) 100%)",
                              "linear-gradient(135deg, rgba(59,130,246,0.2) 0%, rgba(147,51,234,0.1) 100%)"
                            ]
                          }}
                          transition={{ duration: 3, repeat: Infinity }}
                        />
                      )}

                      <Image
                        src={item.icon}
                        alt={item.label}
                        width={22}
                        height={22}
                        className={`relative z-10 transition-all duration-500 ${
                          isActive
                            ? "brightness-125 drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]"
                            : "opacity-70 group-hover:opacity-100 group-hover:brightness-110"
                        }`}
                      />

                      {/* Notification Badge */}
                      <NotificationBadge
                        count={item.notificationCount}
                        show={item.hasNotification}
                        color={isActive ? "blue" : "red"}
                      />

                      {/* Active Indicator Dot */}
                      {isActive && !item.notificationCount && !item.hasNotification && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full shadow-[0_0_10px_rgba(59,130,246,0.8)]"
                          transition={{ type: "spring", damping: 25, stiffness: 300 }}
                        >
                          <div className="absolute inset-0.5 bg-white rounded-full animate-pulse" />
                        </motion.div>
                      )}

                      {/* Ripple Effect */}
                      {isActive && (
                        <motion.div
                          className="absolute inset-0 rounded-2xl border-2 border-blue-400/50"
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 0, 0.5]
                          }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      )}
                    </motion.div>

                    <motion.span
                      className={`text-xs mt-2 font-medium transition-all duration-300 ${
                        isActive
                          ? "text-blue-400 drop-shadow-[0_0_4px_rgba(59,130,246,0.8)]"
                          : "text-gray-400 group-hover:text-gray-200"
                      }`}
                      animate={isActive ? {
                        textShadow: [
                          "0 0 4px rgba(59,130,246,0.8)",
                          "0 0 8px rgba(59,130,246,1)",
                          "0 0 4px rgba(59,130,246,0.8)"
                        ]
                      } : {}}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      {item.label}
                    </motion.span>
                  </Link>
                ) : (
                  <button
                    onClick={() => handleItemClick(item)}
                    className="flex flex-col items-center group"
                  >
                    <motion.div
                      className={`relative p-3 rounded-2xl transition-all duration-500 ${
                        isActive || hasActiveSubmenu
                          ? "bg-gradient-to-br from-blue-500/30 to-purple-500/20 shadow-[0_0_25px_rgba(59,130,246,0.4),0_0_50px_rgba(147,51,234,0.2)] border border-blue-400/30"
                          : "bg-gray-800/20 hover:bg-gray-700/30 border border-transparent hover:border-gray-600/30"
                      }`}
                      whileHover={{
                        scale: 1.15,
                        rotateY: (isActive || hasActiveSubmenu) ? 0 : 15,
                        rotateX: 5,
                        boxShadow: (isActive || hasActiveSubmenu)
                          ? "0 0 40px rgba(59,130,246,0.8), 0 0 80px rgba(147,51,234,0.4), 0 10px 30px rgba(0,0,0,0.3)"
                          : "0 0 25px rgba(255,255,255,0.2), 0 5px 20px rgba(0,0,0,0.2)"
                      }}
                      whileTap={{
                        scale: 0.85,
                        rotateZ: (isActive || hasActiveSubmenu) ? 0 : 5,
                        transition: { duration: 0.1 }
                      }}
                    >
                      {/* Animated Background Gradient */}
                      {(isActive || hasActiveSubmenu) && (
                        <motion.div
                          className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/10"
                          animate={{
                            background: [
                              "linear-gradient(135deg, rgba(59,130,246,0.2) 0%, rgba(147,51,234,0.1) 100%)",
                              "linear-gradient(135deg, rgba(147,51,234,0.2) 0%, rgba(59,130,246,0.1) 100%)",
                              "linear-gradient(135deg, rgba(59,130,246,0.2) 0%, rgba(147,51,234,0.1) 100%)"
                            ]
                          }}
                          transition={{ duration: 3, repeat: Infinity }}
                        />
                      )}

                      <Image
                        src={item.icon}
                        alt={item.label}
                        width={22}
                        height={22}
                        className={`relative z-10 transition-all duration-500 ${
                          isActive || hasActiveSubmenu
                            ? "brightness-125 drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]"
                            : "opacity-70 group-hover:opacity-100 group-hover:brightness-110"
                        }`}
                      />

                      {/* Submenu Indicator */}
                      {item.hasSubmenu && (
                        <motion.div
                          animate={{
                            rotate: hasActiveSubmenu ? 180 : 0,
                            scale: hasActiveSubmenu ? 1.2 : 1
                          }}
                          transition={{ duration: 0.4, type: "spring" }}
                          className="absolute -top-0.5 -right-0.5 w-4 h-4 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center shadow-[0_0_8px_rgba(251,146,60,0.6)]"
                        >
                          <FaChevronUp size={8} className="text-white" />
                        </motion.div>
                      )}

                      {/* Notification Badge */}
                      <NotificationBadge
                        count={item.notificationCount}
                        show={item.hasNotification}
                        color={(isActive || hasActiveSubmenu) ? "blue" : "red"}
                      />

                      {/* Active Indicator Dot */}
                      {(isActive || hasActiveSubmenu) && !item.hasSubmenu && !item.notificationCount && !item.hasNotification && (
                        <motion.div
                          layoutId="activeIndicatorButton"
                          className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full shadow-[0_0_10px_rgba(59,130,246,0.8)]"
                          transition={{ type: "spring", damping: 25, stiffness: 300 }}
                        >
                          <div className="absolute inset-0.5 bg-white rounded-full animate-pulse" />
                        </motion.div>
                      )}

                      {/* Ripple Effect */}
                      {(isActive || hasActiveSubmenu) && (
                        <motion.div
                          className="absolute inset-0 rounded-2xl border-2 border-blue-400/50"
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 0, 0.5]
                          }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      )}
                    </motion.div>

                    <motion.span
                      className={`text-xs mt-2 font-medium transition-all duration-300 ${
                        isActive || hasActiveSubmenu
                          ? "text-blue-400 drop-shadow-[0_0_4px_rgba(59,130,246,0.8)]"
                          : "text-gray-400 group-hover:text-gray-200"
                      }`}
                      animate={(isActive || hasActiveSubmenu) ? {
                        textShadow: [
                          "0 0 4px rgba(59,130,246,0.8)",
                          "0 0 8px rgba(59,130,246,1)",
                          "0 0 4px rgba(59,130,246,0.8)"
                        ]
                      } : {}}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      {item.label}
                    </motion.span>
                  </button>
                )}
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    </>
  );
};

export default BottomBar;
