"use client";
import Header from "./header";
import Sidebar from "./sidebar";
import BottomBar from "./BottomBar";
import { useRejectedStatus } from "@/hooks/useRejectedStatus";
import RejectedStatusModal from "@/components/dashboard/modals/RejectedStatusModal";
import { UserProfileModalProvider, useUserProfileModal } from "@/contexts/UserProfileModalContext";
import UserProfileModal from "@/components/dashboard/modals/UserProfileModal";

const LayoutContent = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const { isModalOpen, closeModal } = useRejectedStatus();
  const { isUserModalOpen, closeUserModal } = useUserProfileModal();

  return (
    <>
      {/* Header - Fixed */}
      <Header />

      {/* Sidebar - Fixed */}
      <Sidebar />

      {/* Main Content */}
      <main className="relative z-10 w-full lg:pr-[264px] pb-20 lg:pb-0 px-5 pt-[100px] min-h-screen">
        {children}
      </main>

      {/* Bottom Bar for Mobile */}
      <BottomBar />

      {/* Rejected Status Modal */}
      <RejectedStatusModal isOpen={isModalOpen} onClose={closeModal} />

      {/* User Profile Modal */}
      <UserProfileModal isOpen={isUserModalOpen} onClose={closeUserModal} />
    </>
  );
};

const Layout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <UserProfileModalProvider>
      <LayoutContent>{children}</LayoutContent>
    </UserProfileModalProvider>
  );
};

export default Layout;
