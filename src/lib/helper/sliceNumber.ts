export const sliceNumber = (number: string | number) => {
  // Convert to number first to handle string inputs properly
  const num = typeof number === 'string' ? parseFloat(number) : number;

  // Check if it's a valid number
  if (isNaN(num)) return '0';

  // Round to appropriate decimal places first to avoid floating point issues
  let roundedNum;

  if (num >= 1000000) {
    // Large numbers: round to 2 decimal places
    roundedNum = Math.round(num * 100) / 100;
  } else if (num >= 1) {
    // Medium numbers: round to 6 decimal places
    roundedNum = Math.round(num * 1000000) / 1000000;
  } else {
    // Small numbers: round to 8 decimal places
    roundedNum = Math.round(num * 100000000) / 100000000;
  }

  // For integers, don't show decimal places
  if (Number.isInteger(roundedNum)) {
    return roundedNum.toLocaleString('en-US');
  }

  // For decimal numbers, show appropriate decimal places
  let maxDecimals = 8;

  if (roundedNum >= 1000000) {
    maxDecimals = 2; // Large numbers: max 2 decimals
  } else if (roundedNum >= 1) {
    maxDecimals = 6; // Medium numbers: max 6 decimals
  } else {
    maxDecimals = 8; // Small numbers: max 8 decimals
  }

  // Format and remove trailing zeros
  const formatted = roundedNum.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: maxDecimals,
  });

  return formatted;
};


