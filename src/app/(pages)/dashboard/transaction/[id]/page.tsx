"use client";

import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { getTransactionDetails } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import Image from "next/image";
import { sliceNumber } from "@/lib/helper/sliceNumber";
import jalaali from "jalaali-js";
import { FiArrowLeft, FiClock, FiCheckCircle, FiInfo, FiDollarSign, FiCreditCard } from "react-icons/fi";

interface TransactionDetail {
  id: number;
  type: string;
  amount: string;
  price: number;
  wallet_id: number;
  wallet_address: string | null;
  currency_id: number;
  user_id: number;
  registrar: number;
  network: string | null;
  status: string;
  description: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  balance_before: string;
  balance_after: string;
  card_id: number | null;
  currency_details: {
    id: number;
    name: string;
    coin_type: string;
    coin_price: string;
  };
  buy_details?: {
    toman_amount: string;
    usd_amount: number;
    usd_rate: string;
    crypto_amount: number;
  };
  sell_details?: {
    crypto_amount: string;
    usd_amount: string;
    usd_rate: string;
    toman_amount: number;
  };
  swap_details?: {
    from_currency: string;
    to_currency: string;
    from_amount: string;
    to_amount: number;
    usd_value: number;
    fee_percentage: string;
    fee_amount: number;
  };
  type_description: string;
  currency: {
    id: number;
    name: string;
    coin_type: string;
    coin_icon: string;
    // Other currency properties...
  };
  card: any | null;
  wallet: any | null;
}

const TransactionDetailPage: React.FC = () => {
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const [transaction, setTransaction] = useState<TransactionDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!params?.id) return;
    fetchTransactionDetails();
  }, [params?.id]);

  const fetchTransactionDetails = async () => {
    setLoading(true);
    try {
      const result = await getTransactionDetails(params.id);
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت جزئیات تراکنش");
      } else {
        setTransaction(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت جزئیات تراکنش");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const jDate = jalaali.toJalaali(date);
      return `${jDate.jy}/${jDate.jm}/${jDate.jd} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (error) {
      return dateString;
    }
  };

  const formatNumber = (value: string | number, decimals: number = 2) => {
    if (typeof value === 'string') {
      // Try to convert string to number
      const numValue = parseFloat(value);
      if (isNaN(numValue)) return value; // Return original if not a valid number
      return numValue.toFixed(decimals);
    }
    return value.toFixed(decimals);
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'done':
        return { text: 'موفق', bgColor: 'bg-[#103923]', icon: <FiCheckCircle className="w-5 h-5 text-green-500" /> };
      case 'pending':
        return { text: 'در انتظار', bgColor: 'bg-[#3A3A3D]', icon: <FiClock className="w-5 h-5 text-yellow-500" /> };
      case 'rejected':
      case 'failed':
        return { text: 'ناموفق', bgColor: 'bg-[#5A1C1E]', icon: <FiInfo className="w-5 h-5 text-red-500" /> };
      default:
        return { text: status, bgColor: 'bg-[#3A3A3D]', icon: <FiInfo className="w-5 h-5 text-gray-500" /> };
    }
  };

  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'buy':
        return { text: 'خرید', color: 'text-[#2FA766]' };
      case 'sell':
        return { text: 'فروش', color: 'text-red-500' };
      case 'swap_in':
        return { text: 'تبدیل (ورودی)', color: 'text-blue-500' };
      case 'swap_out':
        return { text: 'تبدیل (خروجی)', color: 'text-orange-500' };
      case 'withdraw':
        return { text: 'برداشت', color: 'text-purple-500' };
      case 'deposit':
        return { text: 'واریز', color: 'text-green-500' };
      default:
        return { text: type, color: 'text-gray-500' };
    }
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-6 rounded-2xl shadow-lg border border-[#353945]/50 min-h-[400px] flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 border-4 border-[#2FA766] border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-300">در حال بارگذاری جزئیات تراکنش...</p>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-6 rounded-2xl shadow-lg border border-[#353945]/50 min-h-[400px] flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-[#23262F] rounded-full flex items-center justify-center mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-400 text-lg mb-4">تراکنش یافت نشد</p>
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 px-4 py-2 bg-[#23262F] hover:bg-[#2A2D38] text-white rounded-lg transition-colors"
          >
            <FiArrowLeft />
            بازگشت
          </button>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(transaction.status);
  const typeInfo = getTypeInfo(transaction.type);

  return (
    <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-6 rounded-2xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 group relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-[#2FA766] opacity-5 rounded-full -mr-16 -mt-16 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-500 opacity-5 rounded-full -ml-12 -mb-12 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      <div className="flex justify-between items-center mb-6 relative z-10">
        <button
          onClick={() => router.back()}
          className="flex items-center gap-2 px-3 py-1.5 bg-[#23262F] hover:bg-[#2A2D38] text-white rounded-lg transition-colors text-sm"
        >
          <FiArrowLeft className="w-4 h-4" />
          بازگشت
        </button>
        <h1 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
          جزئیات تراکنش
        </h1>
      </div>

      <div className="bg-gradient-to-br from-[#1C1E24]/80 to-[#23262F]/80 p-5 rounded-xl border border-[#353945]/30 mb-6 relative z-10">
        <div className="flex flex-col md:flex-row justify-between items-center mb-4 pb-4 border-b border-[#353945]/30">
          <div className="flex items-center mb-4 md:mb-0">
            <div className="bg-[#353945]/50 p-2 rounded-full mr-3">
              <Image
                className="w-10 h-10"
                src={transaction.currency?.coin_icon ? `https://api.exchangim.com/storage/${transaction.currency.coin_icon}` : "/images/btc-logo.png"}
                height={40}
                width={40}
                alt={transaction.currency_details.coin_type}
                onError={(e) => {
                  e.currentTarget.src = "/images/btc-logo.png";
                }}
              />
            </div>
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                {transaction.currency_details.coin_type}
              </h2>
              <p className="text-sm text-[#B1B5C3]">{transaction.currency_details.name}</p>
            </div>
          </div>
          <div className="flex flex-col items-end">
            <span className={`${statusInfo.bgColor} text-[#C5EFD8] px-3 py-1.5 rounded-lg text-sm flex items-center gap-2`}>
              {statusInfo.icon}
              {statusInfo.text}
            </span>
            <span className="text-sm text-[#B1B5C3] mt-1">
              {formatDate(transaction.created_at)}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-[#B1B5C3] text-sm">نوع تراکنش:</span>
              <span className={`${typeInfo.color} font-medium`}>{typeInfo.text}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-[#B1B5C3] text-sm">مقدار:</span>
              <span className="font-medium">{formatNumber(transaction.amount, 4)} {transaction.currency_details.coin_type}</span>
            </div>

            {transaction.buy_details && (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">مبلغ تومان:</span>
                  <span className="font-medium">{sliceNumber(Math.round(parseFloat(transaction.buy_details.toman_amount.toString())))} تومان</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">مقدار دلار:</span>
                  <span className="font-medium">{formatNumber(transaction.buy_details.usd_amount, 2)} USD</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">نرخ دلار:</span>
                  <span className="font-medium">{sliceNumber(parseFloat(transaction.buy_details.usd_rate).toFixed(0))} تومان</span>
                </div>
              </>
            )}

            {transaction.sell_details && (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">مبلغ تومان:</span>
                  <span className="font-medium">{sliceNumber(Math.round(parseFloat(transaction.sell_details.toman_amount.toString())))} تومان</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">مقدار دلار:</span>
                  <span className="font-medium">{formatNumber(transaction.sell_details.usd_amount, 2)} USD</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">نرخ دلار:</span>
                  <span className="font-medium">{sliceNumber(parseFloat(transaction.sell_details.usd_rate).toFixed(0))} تومان</span>
                </div>
              </>
            )}

            {transaction.swap_details && (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">از ارز:</span>
                  <span className="font-medium">{transaction.swap_details.from_currency}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">به ارز:</span>
                  <span className="font-medium">{transaction.swap_details.to_currency}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">مقدار مبدا:</span>
                  <span className="font-medium">{formatNumber(transaction.swap_details.from_amount, 4)} {transaction.swap_details.from_currency}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">مقدار مقصد:</span>
                  <span className="font-medium">{formatNumber(transaction.swap_details.to_amount, 4)} {transaction.swap_details.to_currency}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">ارزش دلاری:</span>
                  <span className="font-medium">{formatNumber(transaction.swap_details.usd_value, 2)} USD</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#B1B5C3] text-sm">کارمزد:</span>
                  <span className="font-medium">{formatNumber(transaction.swap_details.fee_amount, 4)} ({transaction.swap_details.fee_percentage}%)</span>
                </div>
              </>
            )}
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-[#B1B5C3] text-sm">شناسه تراکنش:</span>
              <span className="font-medium">{transaction.id}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-[#B1B5C3] text-sm">توضیحات:</span>
              <span className="font-medium">{transaction.description}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-[#B1B5C3] text-sm">موجودی قبلی:</span>
              <span className="font-medium">{formatNumber(transaction.balance_before, 2)} {transaction.currency_details.coin_type}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-[#B1B5C3] text-sm">موجودی فعلی:</span>
              <span className="font-medium">{formatNumber(transaction.balance_after, 2)} {transaction.currency_details.coin_type}</span>
            </div>

            {transaction.wallet_address && (
              <div className="flex justify-between items-center">
                <span className="text-[#B1B5C3] text-sm">آدرس کیف پول:</span>
                <span className="font-medium text-xs md:text-sm truncate max-w-[200px]" title={transaction.wallet_address}>
                  {transaction.wallet_address}
                </span>
              </div>
            )}

            {transaction.network && (
              <div className="flex justify-between items-center">
                <span className="text-[#B1B5C3] text-sm">شبکه:</span>
                <span className="font-medium">{transaction.network}</span>
              </div>
            )}

            {transaction.card && (
              <div className="flex justify-between items-center">
                <span className="text-[#B1B5C3] text-sm">کارت:</span>
                <span className="font-medium">{transaction.card.number}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 justify-center mt-8 relative z-10">
        <button
          onClick={() => router.back()}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-[#23262F] hover:bg-[#2A2D38] text-white rounded-xl transition-colors"
        >
          <FiArrowLeft className="w-5 h-5" />
          بازگشت به لیست تراکنش‌ها
        </button>
      </div>
    </div>
  );
};

export default TransactionDetailPage;
