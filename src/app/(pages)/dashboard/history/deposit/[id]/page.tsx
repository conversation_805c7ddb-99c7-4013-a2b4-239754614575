"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { FiArrowRight, <PERSON>C<PERSON>, FiExternalLink } from "react-icons/fi";
import { getDepositTransactionDetails } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";

interface DepositTransactionDetails {
  id: number;
  transaction_id: string;
  address: string;
  from_address: string;
  amount: {
    value: string;
    formatted: string;
    display: string;
  };
  received_amount: {
    value: string;
    formatted: string;
    display: string;
  };
  fees: {
    value: string;
    formatted: string;
    display: string;
  };
  btc_value?: {
    value: string;
    formatted: string;
    display: string;
  };
  usd_value?: {
    value: string;
    formatted: string;
    display: string;
  };
  status: {
    value: number;
    label: string;
    color: string;
  };
  confirmations: {
    current: number;
    required: number;
    percentage: number;
    is_confirmed: boolean;
  };
  coin: {
    id: number;
    name: string;
    coin_type: string;
    icon: string;
    decimal: number;
  };
  network: {
    id: number;
    name: string;
    slug: string;
    chain_id: string;
    block_confirmation: number;
  };
  receiver_wallet: {
    id: number;
    name: string;
    balance: string;
  };
  blockchain_info: {
    block_number: number;
    network_type: string;
    address_type: string;
  };
  admin_info: {
    is_admin_receive: boolean;
    updated_by: any;
    notification_status: string;
  };
  timestamps: {
    created_at: string;
    updated_at: string;
    created_at_persian: string;
    created_at_human: string;
  };
}

const DepositTransactionDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const transactionId = parseInt(params.id as string);
  
  const [details, setDetails] = useState<DepositTransactionDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (transactionId) {
      fetchTransactionDetails();
    }
  }, [transactionId]);

  const fetchTransactionDetails = async () => {
    setLoading(true);
    try {
      const result = await getDepositTransactionDetails(transactionId);
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت جزییات تراکنش");
        router.push('/dashboard/history/deposit');
      } else {
        setDetails(result.data);
      }
    } catch (error) {
      console.error("Error fetching transaction details:", error);
      toast.error("خطا در دریافت جزییات تراکنش");
      router.push('/dashboard/history/deposit');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("کپی شد");
  };

  const getStatusColor = (status: { value: number; color: string }) => {
    switch (status.value) {
      case 1:
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 0:
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 3:
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getStatusDotColor = (status: { value: number; color: string }) => {
    switch (status.value) {
      case 1:
        return 'bg-green-400';
      case 0:
        return 'bg-yellow-400';
      case 3:
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-transparent">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#1C1E24] to-[#18191D] p-6 rounded-2xl mb-6 border border-[#353945]/30">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push('/dashboard/history/deposit')}
              className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
            >
              <FiArrowRight className="w-5 h-5 text-[#B1B5C3]" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-[#FCFCFD] mb-2">جزییات تراکنش واریز</h1>
              <p className="text-[#B1B5C3]">در حال بارگذاری اطلاعات...</p>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="space-y-6 animate-pulse">
          <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gray-600/30 rounded-full"></div>
              <div className="space-y-2 flex-1">
                <div className="h-6 bg-gray-600/30 rounded w-32"></div>
                <div className="h-4 bg-gray-600/20 rounded w-24"></div>
              </div>
              <div className="space-y-2">
                <div className="h-6 bg-gray-600/30 rounded w-24"></div>
                <div className="h-4 bg-gray-600/20 rounded w-16"></div>
              </div>
            </div>
            {Array(8).fill(0).map((_, i) => (
              <div key={i} className="space-y-2 mb-4">
                <div className="h-4 bg-gray-600/20 rounded w-24"></div>
                <div className="h-6 bg-gray-600/30 rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!details) {
    return (
      <div className="min-h-screen bg-transparent">
        <div className="bg-gradient-to-r from-[#1C1E24] to-[#18191D] p-6 rounded-2xl mb-6 border border-[#353945]/30">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push('/dashboard/history/deposit')}
              className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
            >
              <FiArrowRight className="w-5 h-5 text-[#B1B5C3]" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-[#FCFCFD] mb-2">خطا</h1>
              <p className="text-[#B1B5C3]">تراکنش مورد نظر یافت نشد</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-transparent">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#1C1E24] to-[#18191D] p-6 rounded-2xl mb-6 border border-[#353945]/30">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/dashboard/history/deposit')}
            className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors"
          >
            <FiArrowRight className="w-5 h-5 text-[#B1B5C3]" />
          </button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-[#FCFCFD] mb-2">جزییات تراکنش واریز</h1>
            <p className="text-[#B1B5C3]">اطلاعات کامل تراکنش شماره {details.id}</p>
          </div>
          <div className="text-right">
            <div className="text-sm text-[#B1B5C3]">تاریخ ایجاد</div>
            <div className="text-[#FCFCFD] font-semibold">{details.timestamps.created_at_persian}</div>
            <div className="text-xs text-[#B1B5C3]">{details.timestamps.created_at_human}</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Coin Info Card */}
        <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
          <div className="flex items-center gap-4 mb-6">
            <div className="relative">
              <Image
                className="w-16 h-16 rounded-full border-2 border-[#353945]"
                src={`https://api.exchangim.com/storage/${details.coin.icon}`}
                height={64}
                width={64}
                alt={details.coin.coin_type}
                onError={(e) => {
                  e.currentTarget.src = '/images/default-coin.svg';
                }}
              />
            </div>
            <div className="flex-1">
              <div className="text-2xl font-bold text-[#FCFCFD]">
                {details.coin.coin_type}
              </div>
              <div className="text-lg text-[#B1B5C3]">
                {details.coin.name}
              </div>
              <div className="text-sm text-[#B1B5C3]">
                {details.network.name}
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-[#FCFCFD]">
                {details.amount.display}
              </div>
              {details.usd_value && (
                <div className="text-lg text-[#B1B5C3]">
                  {details.usd_value.display}
                </div>
              )}
            </div>
          </div>

          {/* Status and Confirmations */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm text-[#B1B5C3]">وضعیت تراکنش</label>
              <div className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium border ${getStatusColor(details.status)}`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${getStatusDotColor(details.status)}`}></div>
                {details.status.label}
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm text-[#B1B5C3]">تأیید شبکه</label>
              <div className="text-[#FCFCFD] font-medium">
                {details.confirmations.current.toLocaleString()} / {details.confirmations.required.toLocaleString()}
                <span className="text-sm text-[#B1B5C3] mr-2">
                  ({details.confirmations.percentage.toFixed(2)}%)
                </span>
              </div>
              <div className="w-full bg-[#23262F] rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-600 to-blue-700 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(details.confirmations.percentage, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Transaction Details Card */}
        <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
          <h3 className="text-xl font-bold text-[#FCFCFD] mb-6 border-b border-[#353945]/30 pb-3">
            اطلاعات تراکنش
          </h3>

          {/* Transaction ID */}
          <div className="space-y-3 mb-6">
            <label className="text-sm text-[#B1B5C3]">شناسه تراکنش</label>
            <div className="flex items-center gap-2 p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
              <span className="text-sm text-[#FCFCFD] font-mono flex-1 break-all">
                {details.transaction_id}
              </span>
              <button
                onClick={() => copyToClipboard(details.transaction_id)}
                className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors group"
                title="کپی شناسه تراکنش"
              >
                <FiCopy className="w-4 h-4 text-[#B1B5C3] group-hover:text-[#FCFCFD]" />
              </button>
            </div>
          </div>

          {/* Addresses */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="space-y-3">
              <label className="text-sm text-[#B1B5C3]">آدرس مقصد</label>
              <div className="flex items-center gap-2 p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
                <span className="text-sm text-[#FCFCFD] font-mono flex-1 break-all">
                  {details.address}
                </span>
                <button
                  onClick={() => copyToClipboard(details.address)}
                  className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors group"
                  title="کپی آدرس"
                >
                  <FiCopy className="w-4 h-4 text-[#B1B5C3] group-hover:text-[#FCFCFD]" />
                </button>
              </div>
            </div>
            <div className="space-y-3">
              <label className="text-sm text-[#B1B5C3]">آدرس مبدأ</label>
              <div className="flex items-center gap-2 p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
                <span className="text-sm text-[#FCFCFD] font-mono flex-1 break-all">
                  {details.from_address}
                </span>
                <button
                  onClick={() => copyToClipboard(details.from_address)}
                  className="p-2 rounded-lg hover:bg-[#353945]/50 transition-colors group"
                  title="کپی آدرس"
                >
                  <FiCopy className="w-4 h-4 text-[#B1B5C3] group-hover:text-[#FCFCFD]" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Amount Details Card */}
        <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
          <h3 className="text-xl font-bold text-[#FCFCFD] mb-6 border-b border-[#353945]/30 pb-3">
            جزییات مبلغ
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="text-center p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
              <div className="text-sm text-[#B1B5C3] mb-2">مبلغ ارسالی</div>
              <div className="text-xl font-bold text-[#FCFCFD]">
                {details.amount.display}
              </div>
            </div>
            <div className="text-center p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
              <div className="text-sm text-[#B1B5C3] mb-2">مبلغ دریافتی</div>
              <div className="text-xl font-bold text-green-400">
                {details.received_amount.display}
              </div>
            </div>
            <div className="text-center p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
              <div className="text-sm text-[#B1B5C3] mb-2">کارمزد</div>
              <div className="text-xl font-bold text-[#FCFCFD]">
                {details.fees.display}
              </div>
            </div>
          </div>
          {(details.btc_value || details.usd_value) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-[#353945]/30">
              {details.btc_value && (
                <div className="text-center p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
                  <div className="text-sm text-[#B1B5C3] mb-2">ارزش بیت کوین</div>
                  <div className="text-lg font-bold text-orange-400">
                    {details.btc_value.display}
                  </div>
                </div>
              )}
              {details.usd_value && (
                <div className="text-center p-4 bg-[#23262F] rounded-xl border border-[#353945]/30">
                  <div className="text-sm text-[#B1B5C3] mb-2">ارزش دلار</div>
                  <div className="text-lg font-bold text-green-400">
                    {details.usd_value.display}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Blockchain & Wallet Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Blockchain Info */}
          <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
            <h3 className="text-xl font-bold text-[#FCFCFD] mb-6 border-b border-[#353945]/30 pb-3">
              اطلاعات بلاک چین
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
                <span className="text-[#B1B5C3] text-sm">شماره بلاک:</span>
                <span className="text-[#FCFCFD] font-semibold">
                  {details.blockchain_info.block_number.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
                <span className="text-[#B1B5C3] text-sm">نوع شبکه:</span>
                <span className="text-[#FCFCFD] font-semibold">
                  {details.blockchain_info.network_type}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
                <span className="text-[#B1B5C3] text-sm">نوع آدرس:</span>
                <span className="text-[#FCFCFD] font-semibold">
                  {details.blockchain_info.address_type}
                </span>
              </div>
            </div>
          </div>

          {/* Wallet Info */}
          <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
            <h3 className="text-xl font-bold text-[#FCFCFD] mb-6 border-b border-[#353945]/30 pb-3">
              اطلاعات کیف پول
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
                <span className="text-[#B1B5C3] text-sm">نام کیف پول:</span>
                <span className="text-[#FCFCFD] font-semibold">
                  {details.receiver_wallet.name}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
                <span className="text-[#B1B5C3] text-sm">شناسه کیف پول:</span>
                <span className="text-[#FCFCFD] font-semibold">
                  #{details.receiver_wallet.id}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
                <span className="text-[#B1B5C3] text-sm">موجودی کیف پول:</span>
                <span className="text-green-400 font-semibold">
                  {parseFloat(details.receiver_wallet.balance).toFixed(8)} {details.coin.coin_type}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* System Info */}
        <div className="bg-gradient-to-br from-[#1C1E24] to-[#18191D] p-6 rounded-2xl border border-[#353945]/30">
          <h3 className="text-xl font-bold text-[#FCFCFD] mb-6 border-b border-[#353945]/30 pb-3">
            اطلاعات سیستم
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
              <span className="text-[#B1B5C3] text-sm">وضعیت اطلاع‌رسانی:</span>
              <span className="text-[#FCFCFD] font-semibold">
                {details.admin_info.notification_status === 'sent' ? 'ارسال شده' : details.admin_info.notification_status}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
              <span className="text-[#B1B5C3] text-sm">دریافت توسط ادمین:</span>
              <span className="text-[#FCFCFD] font-semibold">
                {details.admin_info.is_admin_receive ? 'بله' : 'خیر'}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[#23262F] rounded-lg">
              <span className="text-[#B1B5C3] text-sm">آخرین بروزرسانی:</span>
              <span className="text-[#FCFCFD] font-semibold">
                {new Date(details.timestamps.updated_at).toLocaleDateString('fa-IR')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepositTransactionDetailsPage;
