"use client";

import React, { useEffect, useState } from "react";
import { getProfile, updateProfile } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";
import { motion } from "framer-motion";
import { FaUser, FaIdCard, FaCalendarAlt, FaPhone, FaEdit, FaSave, FaTimes } from "react-icons/fa";
import Image from "next/image";

interface IUserProfile {
  national_id?: string;
  firstname?: string;
  lastname?: string;
  birth_date?: string;
  phone?: string;
  email?: string;
  address?: string;
}

const PersonalInfoPage: React.FC = () => {
  const [info, setInfo] = useState<IUserProfile>({});
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedInfo, setEditedInfo] = useState<IUserProfile>({});
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    getProfileHandler();
  }, []);

  async function getProfileHandler() {
    setLoading(true);
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
        setEditedInfo(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات");
    } finally {
      setLoading(false);
    }
  }

  const handleEdit = () => {
    setIsEditing(true);
    setEditedInfo({ ...info });
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedInfo({ ...info });
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // اینجا باید API برای به‌روزرسانی اطلاعات فراخوانی شود
      // const result = await updateProfile(editedInfo);
      // if (result.isError) {
      //   toast.error("خطا در به‌روزرسانی اطلاعات");
      // } else {
        setInfo(editedInfo);
        setIsEditing(false);
        toast.success("اطلاعات با موفقیت به‌روزرسانی شد");
      // }
    } catch (error) {
      toast.error("خطا در به‌روزرسانی اطلاعات");
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof IUserProfile, value: string) => {
    setEditedInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6">
      <div className="bg-[#18191D] rounded-2xl p-4 md:p-6 shadow-lg">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-blue-600 to-blue-500 p-2 rounded-lg shadow-[0_0_15px_rgba(59,130,246,0.3)]">
              <FaUser className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold text-white">اطلاعات شخصی</h1>
          </div>
          
          <div className="flex gap-2">
            {!isEditing ? (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleEdit}
                className="bg-gradient-to-r from-blue-600 to-blue-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:from-blue-500 hover:to-blue-400 transition-all"
              >
                <FaEdit className="w-4 h-4" />
                <span>ویرایش</span>
              </motion.button>
            ) : (
              <div className="flex gap-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSave}
                  disabled={saving}
                  className="bg-gradient-to-r from-green-600 to-green-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:from-green-500 hover:to-green-400 transition-all disabled:opacity-50"
                >
                  <FaSave className="w-4 h-4" />
                  <span>{saving ? "در حال ذخیره..." : "ذخیره"}</span>
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleCancel}
                  className="bg-gradient-to-r from-red-600 to-red-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:from-red-500 hover:to-red-400 transition-all"
                >
                  <FaTimes className="w-4 h-4" />
                  <span>لغو</span>
                </motion.button>
              </div>
            )}
          </div>
        </div>

        {/* Profile Avatar Section */}
        <div className="flex flex-col items-center mb-8">
          <div className="relative">
            <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
              <FaUser className="w-12 h-12 text-white" />
            </div>
            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-[#18191D] flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
          </div>
          <h2 className="text-xl font-bold text-white mt-4">
            {info.firstname && info.lastname 
              ? `${info.firstname} ${info.lastname}` 
              : "کاربر مهمان"
            }
          </h2>
          <p className="text-gray-400 text-sm">{info.phone || "شماره تلفن موجود نیست"}</p>
        </div>

        {/* Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          {/* نام */}
          <InfoCard
            icon={<FaUser className="w-5 h-5 text-blue-400" />}
            label="نام"
            value={info.firstname}
            isEditing={isEditing}
            onEdit={(value) => handleInputChange('firstname', value)}
            editValue={editedInfo.firstname}
          />

          {/* نام خانوادگی */}
          <InfoCard
            icon={<FaUser className="w-5 h-5 text-blue-400" />}
            label="نام خانوادگی"
            value={info.lastname}
            isEditing={isEditing}
            onEdit={(value) => handleInputChange('lastname', value)}
            editValue={editedInfo.lastname}
          />

          {/* کد ملی */}
          <InfoCard
            icon={<FaIdCard className="w-5 h-5 text-green-400" />}
            label="کد ملی"
            value={info.national_id}
            isEditing={false} // کد ملی قابل ویرایش نیست
            onEdit={() => {}}
            editValue=""
          />

          {/* شماره تلفن */}
          <InfoCard
            icon={<FaPhone className="w-5 h-5 text-purple-400" />}
            label="شماره تلفن"
            value={info.phone}
            isEditing={false} // شماره تلفن قابل ویرایش نیست
            onEdit={() => {}}
            editValue=""
          />

          {/* تاریخ تولد */}
          <InfoCard
            icon={<FaCalendarAlt className="w-5 h-5 text-orange-400" />}
            label="تاریخ تولد"
            value={info.birth_date}
            isEditing={isEditing}
            onEdit={(value) => handleInputChange('birth_date', value)}
            editValue={editedInfo.birth_date}
          />

          {/* ایمیل */}
          <InfoCard
            icon={<FaUser className="w-5 h-5 text-pink-400" />}
            label="ایمیل"
            value={info.email}
            isEditing={isEditing}
            onEdit={(value) => handleInputChange('email', value)}
            editValue={editedInfo.email}
          />
        </div>

        {/* Authentication Status */}
        <div className="mt-8 bg-gradient-to-r from-blue-600/10 to-indigo-600/10 rounded-xl p-4 border border-blue-500/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-500 rounded-lg flex items-center justify-center">
                <FaIdCard className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-medium">وضعیت احراز هویت</h3>
                <p className="text-gray-400 text-sm">برای دسترسی کامل احراز هویت کنید</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-400 text-sm bg-green-400/10 px-3 py-1 rounded-full">
                تایید شده
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface InfoCardProps {
  icon: React.ReactNode;
  label: string;
  value?: string;
  isEditing: boolean;
  onEdit: (value: string) => void;
  editValue?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  icon,
  label,
  value,
  isEditing,
  onEdit,
  editValue
}) => {
  const isReadOnly = label === "کد ملی" || label === "شماره تلفن";

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`bg-[#1C1E24] rounded-xl p-4 border transition-all ${
        isReadOnly
          ? "border-gray-800/50 opacity-75"
          : "border-gray-800 hover:border-gray-700"
      }`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            isReadOnly ? "bg-gray-800/50" : "bg-gray-800"
          }`}>
            {icon}
          </div>
          <span className="text-gray-400 text-sm font-medium">{label}</span>
        </div>
        {isReadOnly && (
          <span className="text-xs text-gray-500 bg-gray-800/50 px-2 py-1 rounded-full">
            غیرقابل ویرایش
          </span>
        )}
      </div>

      {isEditing && !isReadOnly ? (
        <input
          type="text"
          value={editValue || ""}
          onChange={(e) => onEdit(e.target.value)}
          className="w-full bg-[#23262F] text-white px-3 py-2 rounded-lg border border-gray-700 focus:border-blue-500 focus:outline-none transition-colors text-sm md:text-base"
          placeholder={`${label} را وارد کنید`}
        />
      ) : (
        <div className="bg-[#23262F]/50 rounded-lg p-3">
          <p className={`font-medium text-sm md:text-base ${
            value ? "text-white" : "text-gray-500"
          }`}>
            {value || "وارد نشده"}
          </p>
        </div>
      )}
    </motion.div>
  );
};

export default PersonalInfoPage;
