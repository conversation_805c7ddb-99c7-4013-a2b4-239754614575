"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import JsonLd from '@/components/seo/JsonLd';
import ContactSeoContent from './ContactSeoContent';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import BtnLoader from '@/components/form/BtnLoader';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // BreadcrumbList schema
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'صفحه اصلی',
        item: 'https://exchangim.com'
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'تماس با ما',
        item: 'https://exchangim.com/contact'
      }
    ]
  };

  // ContactPage schema
  const contactSchema = {
    '@context': 'https://schema.org',
    '@type': 'ContactPage',
    name: 'تماس با اکسچنجیم',
    description: 'راه‌های ارتباط با صرافی ارز دیجیتال اکسچنجیم',
    url: 'https://exchangim.com/contact',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '021-12345678',
      contactType: 'customer service',
      email: '<EMAIL>',
      areaServed: 'IR',
      availableLanguage: ['Persian', 'English'],
      hoursAvailable: 'Mo-Fr 09:00-18:00'
    }
  };

  // Organization schema
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'اکسچنجیم',
    url: 'https://exchangim.com',
    logo: 'https://exchangim.com/images/main-logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '021-12345678',
      contactType: 'customer service',
      email: '<EMAIL>',
      areaServed: 'IR',
      availableLanguage: ['Persian', 'English']
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'IR',
      addressLocality: 'تهران',
      streetAddress: 'تهران، هفت تیر'
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validation
    if (!formData.name || !formData.email || !formData.phone || !formData.subject || !formData.message) {
      toast.error('لطفا همه فیلدها را پر کنید');
      setIsSubmitting(false);
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('لطفا ایمیل معتبر وارد کنید');
      setIsSubmitting(false);
      return;
    }

    // Phone validation (Iranian phone number)
    const phoneRegex = /^(\+98|0)?9\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      toast.error('لطفا شماره تلفن معتبر وارد کنید');
      setIsSubmitting(false);
      return;
    }

    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('پیام شما با موفقیت ارسال شد. به زودی با شما تماس خواهیم گرفت');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      toast.error('خطایی در ارسال پیام رخ داد. لطفا دوباره تلاش کنید');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-[#141416] min-h-screen">
      {/* Structured Data */}
      <JsonLd data={breadcrumbSchema} />
      <JsonLd data={contactSchema} />
      <JsonLd data={organizationSchema} />

      <div className="flex justify-center w-full bg-transparent fixed top-0 left-0 right-0 z-50">
        <div className="w-full max-w-[1100px]">
          <Navbar
            className="py-4 border-b border-gray-800 backdrop-blur-sm bg-transparent"
            hideSearch
            hideSignUp
          />
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 max-w-7xl text-[#FCFCFD] pt-24">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-white mb-4">تماس با ما</h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            ما همیشه آماده پاسخگویی به سوالات و درخواست‌های شما هستیم. از طریق فرم زیر یا راه‌های ارتباطی با ما در تماس باشید.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-[#18191D] p-8 rounded-2xl"
          >
            <h2 className="text-2xl font-bold text-white mb-6">ارسال پیام</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    نام و نام خانوادگی *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full p-3 bg-[#23262F] border border-gray-700 rounded-lg text-white focus:border-blue-500 focus:outline-none transition-colors"
                    placeholder="نام خود را وارد کنید"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    شماره تلفن *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full p-3 bg-[#23262F] border border-gray-700 rounded-lg text-white focus:border-blue-500 focus:outline-none transition-colors"
                    placeholder="09123456789"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  ایمیل *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-[#23262F] border border-gray-700 rounded-lg text-white focus:border-blue-500 focus:outline-none transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  موضوع *
                </label>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-[#23262F] border border-gray-700 rounded-lg text-white focus:border-blue-500 focus:outline-none transition-colors"
                  placeholder="موضوع پیام خود را وارد کنید"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  پیام *
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows="5"
                  className="w-full p-3 bg-[#23262F] border border-gray-700 rounded-lg text-white focus:border-blue-500 focus:outline-none transition-colors resize-none"
                  placeholder="پیام خود را اینجا بنویسید..."
                ></textarea>
              </div>

              <BtnLoader
                type="submit"
                pending={isSubmitting}
                onClick={() => {}}
                label="ارسال پیام"
                className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              />
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-8"
          >
            {/* Contact Info Cards */}
            <div className="bg-[#18191D] p-8 rounded-2xl">
              <h2 className="text-2xl font-bold text-white mb-6">اطلاعات تماس</h2>
              <div className="space-y-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center ml-4">
                    <img src="/images/phone-icon.png" alt="تلفن" className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">تلفن تماس</h3>
                    <a href="tel:02112345678" className="text-gray-400 hover:text-blue-400 transition-colors">
                      021-12345678
                    </a>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center ml-4">
                    <img src="/images/email-icon.png" alt="ایمیل" className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">ایمیل</h3>
                    <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-blue-400 transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center ml-4">
                    <svg className="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-white font-medium">آدرس</h3>
                    <p className="text-gray-400">تهران، هفت تیر</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center ml-4">
                    <svg className="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-white font-medium">ساعات کاری</h3>
                    <p className="text-gray-400">شنبه تا پنج‌شنبه: ۹:۰۰ - ۱۸:۰۰</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="bg-[#18191D] p-8 rounded-2xl">
              <h2 className="text-2xl font-bold text-white mb-6">لینک‌های مفید</h2>
              <div className="space-y-4">
                <Link href="/support" className="block text-gray-400 hover:text-blue-400 transition-colors">
                  • پشتیبانی و راهنما
                </Link>
                <Link href="/faq" className="block text-gray-400 hover:text-blue-400 transition-colors">
                  • سوالات متداول
                </Link>
                <Link href="/privacy" className="block text-gray-400 hover:text-blue-400 transition-colors">
                  • قوانین و حریم خصوصی
                </Link>
                <Link href="/dashboard" className="block text-gray-400 hover:text-blue-400 transition-colors">
                  • ورود به حساب کاربری
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* SEO Content Section */}
      <ContactSeoContent />

      <Footer />
    </div>
  );
};

export default ContactPage;
