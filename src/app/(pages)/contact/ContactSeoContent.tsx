'use client';

import { motion } from "framer-motion";

export default function ContactSeoContent() {
  return (
    <section className="py-16 bg-[#18191D] text-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          <h2 className="text-3xl font-bold mb-8 text-center">تماس با صرافی ارز دیجیتال اکسچنجیم</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div>
              <h3 className="text-xl font-semibold mb-4 text-blue-400">چرا با ما تماس بگیرید؟</h3>
              <ul className="space-y-3 text-gray-300">
                <li>• پشتیبانی ۲۴ ساعته و ۷ روز هفته</li>
                <li>• راهنمایی در خرید و فروش ارز دیجیتال</li>
                <li>• حل مشکلات فنی و تراکنش‌ها</li>
                <li>• مشاوره در انتخاب بهترین ارزهای دیجیتال</li>
                <li>• آموزش استفاده از پلتفرم</li>
                <li>• پاسخ به سوالات امنیتی</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-4 text-blue-400">خدمات پشتیبانی ما</h3>
              <ul className="space-y-3 text-gray-300">
                <li>• راهنمایی احراز هویت</li>
                <li>• کمک در واریز و برداشت</li>
                <li>• حل مشکلات حساب کاربری</li>
                <li>• آموزش امنیت دیجیتال</li>
                <li>• پشتیبانی API و توسعه‌دهندگان</li>
                <li>• مشاوره سرمایه‌گذاری</li>
              </ul>
            </div>
          </div>

          <div className="bg-[#141416] p-8 rounded-2xl mb-8">
            <h3 className="text-2xl font-semibold mb-6 text-center">راه‌های مختلف ارتباط با اکسچنجیم</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                </div>
                <h4 className="font-semibold mb-2">تماس تلفنی</h4>
                <p className="text-gray-400 text-sm">برای دریافت پشتیبانی فوری</p>
                <p className="text-blue-400 mt-2">021-12345678</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <h4 className="font-semibold mb-2">ایمیل</h4>
                <p className="text-gray-400 text-sm">برای سوالات تخصصی</p>
                <p className="text-blue-400 mt-2"><EMAIL></p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                  </svg>
                </div>
                <h4 className="font-semibold mb-2">چت آنلاین</h4>
                <p className="text-gray-400 text-sm">پاسخ سریع در داشبورد</p>
                <p className="text-blue-400 mt-2">۲۴/۷ فعال</p>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h3 className="text-xl font-semibold mb-4">زمان پاسخگویی</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="bg-[#141416] p-4 rounded-lg">
                <h4 className="font-semibold text-green-400 mb-2">تماس تلفنی</h4>
                <p className="text-gray-400">شنبه تا پنج‌شنبه: ۹:۰۰ - ۱۸:۰۰</p>
                <p className="text-gray-400">پاسخگویی فوری</p>
              </div>
              <div className="bg-[#141416] p-4 rounded-lg">
                <h4 className="font-semibold text-blue-400 mb-2">ایمیل و فرم تماس</h4>
                <p className="text-gray-400">حداکثر ۲۴ ساعت</p>
                <p className="text-gray-400">پاسخ کامل و تخصصی</p>
              </div>
            </div>
          </div>

          <div className="mt-12 text-center">
            <h3 className="text-xl font-semibold mb-4">سوالات متداول</h3>
            <p className="text-gray-400 mb-6">
              قبل از تماس با ما، ممکن است پاسخ سوال خود را در بخش سوالات متداول پیدا کنید.
            </p>
            <a 
              href="/faq" 
              className="inline-block bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300"
            >
              مشاهده سوالات متداول
            </a>
          </div>

          <div className="mt-12 p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl border border-blue-500/20">
            <h3 className="text-xl font-semibold mb-4 text-center">تعهد ما به شما</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl mb-2">🔒</div>
                <h4 className="font-semibold mb-2">امنیت بالا</h4>
                <p className="text-sm text-gray-400">حفاظت از اطلاعات شخصی شما</p>
              </div>
              <div>
                <div className="text-2xl mb-2">⚡</div>
                <h4 className="font-semibold mb-2">پاسخ سریع</h4>
                <p className="text-sm text-gray-400">کمترین زمان انتظار</p>
              </div>
              <div>
                <div className="text-2xl mb-2">🎯</div>
                <h4 className="font-semibold mb-2">راهنمایی دقیق</h4>
                <p className="text-sm text-gray-400">حل کامل مشکلات شما</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
