import { Suspense } from "react";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-cover bg-center bg-no-repeat bg-fixed"
         style={{ backgroundImage: "url('/images/main-bg.jpg')" }}>
      {/* Background overlay */}
      <div className="absolute inset-0 bg-[#18191D]/70 backdrop-blur-md"></div>

      {/* Animated particles */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      {/* Responsive container */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <Suspense fallback={null}>
          {children}
        </Suspense>
      </div>
    </div>
  );
}
