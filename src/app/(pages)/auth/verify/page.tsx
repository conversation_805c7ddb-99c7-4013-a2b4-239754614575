"use client";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { verifyOtp } from "@/requests/authRequest";
import { resendOtp } from "@/actions/clientActions";
import toast from "react-hot-toast";
import { Auth<PERSON>ontainer, AuthHeader, AuthOTP, AuthFooter } from "@/components/auth";

const Verify = () => {
  const [smsCode, setSmsCode] = useState("");
  const [error, setError] = useState("");
  const [timeLeft, setTimeLeft] = useState(120);
  const [isResending, setIsResending] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const phone = searchParams.get("phone");

  useEffect(() => {
    if (!phone) {
      router.push("/auth/login");
    }
  }, [phone, router]);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
      return () => clearInterval(timer);
    }
  }, [timeLeft]);

  useEffect(() => {
    if (smsCode.length === 6) {
      handleSubmit();
    }
  }, [smsCode]);

  const handleSubmit = async () => {
    setError("");
    const result = await verifyOtp(phone, smsCode);
    if (result.isError) {
      toast.error("خطایی رخ داد لطفا دو دقیقه دیگر تلاش فرمایید");
    } else {
      toast.success(result.message);
      router.replace(`/dashboard`);
    }
  };

  const handleResendOtp = async () => {
    if (timeLeft > 0 || !phone) return;

    setIsResending(true);
    try {
      // Format phone number (remove leading zero if present)
      const formattedPhone = phone.startsWith('0') ? phone.substring(1) : phone;

      const result = await resendOtp(formattedPhone);
      if (result.isError) {
        toast.error("خطایی در ارسال مجدد کد رخ داد. لطفا دوباره تلاش کنید.");
      } else {
        toast.success("کد تایید جدید ارسال شد.");
        setTimeLeft(120); // Reset timer to 2 minutes
        setSmsCode(""); // Clear input field
      }
    } catch (error) {
      toast.error("خطایی در ارسال مجدد کد رخ داد. لطفا دوباره تلاش کنید.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <AuthContainer maxWidth="md">
      <form action={handleSubmit}>
        <AuthHeader
          title="تایید کد پیامکی"
          subtitle="کد ۶ رقمی ارسال شده را وارد کنید"
          logoType="dots"
        />

        <AuthOTP
          title=""
          subtitle=""
          value={smsCode}
          onChange={setSmsCode}
          delay={0.6}
        />

        <AuthFooter
          buttonLabel="تایید کد"
          buttonType="submit"
          showTimer
          timeLeft={timeLeft}
          onResend={handleResendOtp}
          isResending={isResending}
          resendText="هنوز کد را دریافت نکردید؟"
          error={error}
          delay={0.7}
        />
      </form>
    </AuthContainer>
  );
};

export default Verify;
