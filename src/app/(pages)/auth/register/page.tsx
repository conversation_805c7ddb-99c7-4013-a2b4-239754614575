"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { register } from "@/requests/authRequest";
import toast from "react-hot-toast";
import { AuthContainer, AuthHeader, AuthInput, AuthSelect, AuthOTP, AuthFooter } from "@/components/auth";

export default function Register() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const phone = searchParams.get("phone");
  const [firstname, setFirstname] = useState<string>("");
  const [lastname, setLastname] = useState<string>("");
  const [gender, setGender] = useState<string>("");
  const [national_id, setNational_id] = useState<string>("");
  const [ref, setRef] = useState<string>("");
  const [smsCode, setSmsCode] = useState("");

  const [timeLeft, setTimeLeft] = useState(30);
  const [timerRunning, setTimerRunning] = useState(true);

  useEffect(() => {
    if (!phone) {
      router.push("/auth/login");
    }
  }, [phone, router]);

  useEffect(() => {
    if (timerRunning && timeLeft > 0) {
      const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
      return () => clearInterval(timer);
    } else if (timeLeft === 0) {
      setTimerRunning(false);
    }
  }, [timeLeft, timerRunning]);

  async function handleSubmit() {
    const refToSend = ref && ref.trim() !== "" ? ref : undefined;
    const result = await register(
      smsCode,
      phone,
      firstname,
      lastname,
      gender,
      national_id,
      refToSend
    );
    if (result.isError) {
      toast.error("خطایی رخ خطایی رخ داد لطفا دو دقیقه دیگر تلاش فرمایید");
    } else {
      toast.success(result.message);
      router.push("/dashboard");
    }
  }

  return (
    <AuthContainer
      maxWidth="lg"
      showBackButton
      onBackClick={() => router.push("/auth/login")}
      backButtonText="بازگشت"
    >
      <form action={handleSubmit}>
        <AuthHeader
          title="ثبت نام"
          logoType="icon"
        />

        <div className="space-y-3 sm:space-y-4">
          <AuthInput
            type="text"
            name="firstName"
            placeholder="نام"
            value={firstname}
            onChange={(e) => setFirstname(e.target.value)}
            delay={0.5}
            animationDirection="left"
          />

          <AuthInput
            type="text"
            name="lastName"
            placeholder="نام خانوادگی"
            value={lastname}
            onChange={(e) => setLastname(e.target.value)}
            delay={0.6}
            animationDirection="right"
          />

          <AuthSelect
            name="gender"
            placeholder="انتخاب جنسیت"
            value={gender}
            onChange={(e) => setGender(e.target.value)}
            options={[
              { value: "male", label: "مرد" },
              { value: "female", label: "زن" }
            ]}
            delay={0.7}
            animationDirection="left"
          />

          <AuthInput
            type="tel"
            name="nationalId"
            placeholder="کد ملی"
            value={national_id}
            onChange={(e) => setNational_id(e.target.value)}
            maxLength={10}
            delay={0.8}
            animationDirection="right"
          />

          <AuthInput
            type="text"
            name="ref"
            placeholder="کد دعوت (اختیاری)"
            value={ref}
            onChange={(e) => setRef(e.target.value)}
            delay={0.9}
            animationDirection="left"
          />
        </div>

        <AuthOTP
          title="کد امنیت پیامکی"
          subtitle="ما یک کد تایید 6 رقمی به شما پیامک کرده‌ایم. لطفاً کد را در کادر زیر وارد کنید تا درخواست ورود خود را تایید کنید"
          value={smsCode}
          onChange={setSmsCode}
          delay={1.0}
        />

        <AuthFooter
          buttonLabel="ثبت نام"
          buttonType="submit"
          showTimer
          timeLeft={timeLeft}
          onResend={() => router.push("/auth/login")}
          resendText="کد ورود را دریافت نکردید؟"
          delay={1.1}
        />
      </form>
    </AuthContainer>
  );
}
