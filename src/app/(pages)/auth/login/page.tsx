"use client";
import { useState, ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { loginUser } from "@/requests/authRequest";
import toast from "react-hot-toast";
import { Auth<PERSON><PERSON><PERSON>, Auth<PERSON>eader, AuthInput, AuthFooter } from "@/components/auth";

const Login = () => {
  const [phone, setPhone] = useState<string>("");
  const [error, setError] = useState<string>("");
  const router = useRouter();

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPhone(value);

    // Accept 10 digits or 11 digits with leading zero
    const phonePattern = /^(0?[0-9]{10})$/;
    if (phonePattern.test(value)) {
      setError("");
    } else {
      setError("لطفا یک شماره تلفن معتبر وارد کنید.");
    }
  };

  const handleLogin = async () => {
    // Remove leading zero if present
    const formattedPhone = phone.startsWith('0') ? phone.substring(1) : phone;

    const result = await loginUser(formattedPhone);
    if (result.isError) {
      toast.error("خطایی رخ داد لطفا دو دقیقه دیگر تلاش فرمایید");
    } else {
      if (result.status) {
        router.replace(`/auth/verify?phone=${formattedPhone}`);
      } else {
        router.push(`/auth/register?phone=${formattedPhone}`);
      }
      toast.success(result.message);
    }
  };

  return (
    <AuthContainer maxWidth="md">
      <form action={handleLogin}>
        <AuthHeader
          title="به اکسچنجیم خوش آمدید"
          subtitle="لطفا شماره تلفن خود را برای ورود یا ثبت نام وارد کنید"
          logoType="dots"
        />

        <AuthInput
          type="tel"
          autoFocus
          placeholder="شماره تماس"
          value={phone}
          onChange={handleInputChange}
          icon={
            <img
              src="/images/phone-icon.png"
              className="w-4 h-4 sm:w-5 sm:h-5 bg-transparent"
              alt="Phone icon"
            />
          }
          delay={0.6}
        />

        <AuthFooter
          buttonLabel="مرحله بعد"
          buttonType="submit"
          error={error}
          delay={0.7}
        />
      </form>
    </AuthContainer>
  );
};

export default Login;
