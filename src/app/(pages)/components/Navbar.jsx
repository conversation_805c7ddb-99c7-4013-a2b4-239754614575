"use client";
import Link from "next/link";
import { useState, useEffect } from "react";
import { FaSearch } from "react-icons/fa";
import { FaBars } from "react-icons/fa";

export default function Navbar({
  hideSearch = false,
  hideSignUp = false,
  className = "",
}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // بررسی وجود توکن در کوکی‌ها
    const checkAuth = () => {
      const token = document.cookie
        .split("; ")
        .find((row) => row.startsWith("token="));

      if (token) {
        setIsLoggedIn(true);
      }
    };

    checkAuth();
  }, []);

  const handleMenuClick = (href) => {
    setIsMenuOpen(false);
    window.location.href = href;
  };

  return (
    <nav
      className={`text-white text-normal p-4 flex items-center justify-between relative font-abril bg-[#141416]/40 h-[55px] ${className}`}
    >
      {/* Search and login buttons - left side */}
      <div className="flex items-center space-x-4 bg-transparent">
        {!hideSearch && (
          <button className="p-2 bg-[#23262F] rounded hover:bg-gray-700">
            <FaSearch className="text-white bg-transparent" />
          </button>
        )}
        {!hideSignUp &&
          (isLoggedIn ? (
            <Link href="/dashboard">
              <button
                type="button"
                className="bg-blue-400 px-4 py-2 rounded text-white text-sm cursor-pointer"
              >
                داشبورد کاربری
              </button>
            </Link>
          ) : (
            <Link href="/auth/login">
              <button
                type="button"
                className="bg-blue-400 px-4 py-2 rounded text-white text-sm cursor-pointer"
              >
                ورود / ثبت‌نام
              </button>
            </Link>
          ))}
      </div>
      {/* Right side container */}
      <div className="flex items-center justify-end bg-transparent">
        {/* Desktop menu - next to logo */}
        <div className="hidden md:flex space-x-6 text-sm bg-transparent ml-6">
          <Link href="/blog" className="hover:text-gray-400">
            وبلاگ
          </Link>
          <Link href="/contact" className="hover:text-gray-400">
            تماس با ما
          </Link>
          <Link href="/dashboard/price" className="hover:text-gray-400 ">
            قیمت لحظه‌ای
          </Link>
          <Link href="" className="hover:text-gray-400 mr-2">
            خرید و فروش رمز ارز
          </Link>
        </div>
        {/* Logo and mobile menu button */}
        <div className="flex items-center bg-transparent">
          <div className="md:hidden mr-4">
            <button
              className={`text-2xl m-3 ${
                isMenuOpen ? "text-white" : "text-gray-400"
              }`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <FaBars />
            </button>
          </div>
          <img src="/images/main-logo.png" alt="Logo" className="h-12 w-40" />
        </div>
      </div>
      {/* Mobile menu dropdown */}
      {isMenuOpen && (
        <div className="absolute top-16 right-0 w-full bg-gray-800 text-white p-4 space-y-4 md:hidden text-center z-10">
          <Link
            href="#"
            className="block hover:text-gray-400 bg-transparent"
            onClick={() => handleMenuClick("#")}
          >
            خرید و فروش رمز ارز
          </Link>
          <Link
            href="#/dashboard/price"
            className="block hover:text-gray-400 bg-transparent"
            onClick={() => handleMenuClick("#")}
          >
            قیمت لحظه‌ای
          </Link>
          <Link
            href="/blog"
            className="block hover:text-gray-400 bg-transparent"
            onClick={() => handleMenuClick("#")}
          >
            وبلاگ
          </Link>
          <Link
            href="/contact"
            className="block hover:text-gray-400 bg-transparent"
            onClick={() => handleMenuClick("/contact")}
          >
            تماس با ما
          </Link>
        </div>
      )}
    </nav>
  );
}
