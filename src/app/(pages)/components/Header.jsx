"use client";

import { useState, useEffect } from "react";
import { FaEnvelope, FaUsers, FaBitcoin, FaChartLine, FaShieldAlt, FaHeadset, FaPercentage, FaBolt } from "react-icons/fa";
import Link from "next/link";
import { motion } from "framer-motion";

export default function Header() {
  const [phone, setPhone] = useState("");
  const [counters, setCounters] = useState({ users: 0, crypto: 0, volume: 0 });
  const [activeSlide, setActiveSlide] = useState(0);

  // Features list
  const features = [
    "امنیت بالا در معاملات",
    "پشتیبانی ۲۴ ساعته",
    "کارمزد پایین",
    "سرعت بالای تراکنش‌ها"
  ];

  // Animate counters and auto-slide
  useEffect(() => {
    // Counter animation
    const counterInterval = setInterval(() => {
      setCounters(prev => ({
        users: prev.users < 500 ? prev.users + 5 : 500,
        crypto: prev.crypto < 1000 ? prev.crypto + 10 : 1000,
        volume: prev.volume < 25 ? prev.volume + 0.25 : 25,
      }));
    }, 30);

    // Auto-slide animation
    const slideInterval = setInterval(() => {
      setActiveSlide(prev => (prev + 1) % features.length);
    }, 3000);

    return () => {
      clearInterval(counterInterval);
      clearInterval(slideInterval);
    };
  }, [features.length]);

  return (
    <motion.header
      initial={{ opacity: 5 }}
      animate={{ opacity: 0.9 }}
      transition={{ duration: 1.8 }}
      className="text-white py-10 text-center relative bg-[url('/image/main-bg.jpg')] bg-cover bg-center bg-fixed bg-no-repeat overflow-hidden"
      dir="rtl"
    >
      {/* Animated background particles */}
      <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>

      <div className="container mx-auto px-4 bg-transparent relative z-10">
        {/* Header with Text and Crypto Image */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="flex flex-col md:flex-row items-center justify-center gap-6 bg-transparent"
        >
          {/* Text and Image Section */}
          <motion.div
            className="w-full flex flex-col md:flex-row items-center justify-center gap-4 md:gap-8 mb-4"
          >
            {/* Right Side - Text */}
            <motion.div className="order-2 md:order-1 text-center md:text-right">
              <motion.h1
                className="bg-transparent font-abril text-[40px] leading-tight font-normal md:text-[40px] sm:text-[30px] xs:text-[25px]"
              >
                هوشمندانه معامله کن
                <br />
                با <motion.span
                      whileHover={{ scale: 1.05 }}
                      className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-blue-600 inline-block"
                    >
                      اکسچنجیم
                    </motion.span> سریع‌تر
                رشد کن
              </motion.h1>

              {/* Mobile Number Section - Moved inside text section */}
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col md:flex-row items-center justify-center md:justify-start gap-4 mt-6"
              >
                {/* <div className="relative w-full md:w-80 lg:w-96 h-12 group">
                  <input
                    type="text"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="شماره موبایل خود را وارد کنید"
                    className="w-full h-full pr-10 pl-4 text-white rounded-lg bg-gray-800/60 backdrop-blur-md border border-gray-700/50 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/30 transition-all duration-300 text-right"
                  />
                  <FaEnvelope className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-blue-400 text-xl bg-transparent transition-colors duration-300" />
                </div>

                <Link href="/auth/login" className="w-full md:w-auto">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full md:w-auto h-12 text-white px-8 rounded-lg bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 font-medium"
                  >
                    عضویت سریع
                  </motion.button>
                </Link> */}
              </motion.div>
            </motion.div>

            {/* Left Side - Crypto Image */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="order-1 md:order-2 flex justify-center"
            >
              <motion.div
                animate={{
                  y: [0, -10, 0]
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="relative flex items-center justify-center"
              >
                {/* Simple glow effect behind image */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl blur-xl -z-10 scale-110"></div>

                {/* Crypto Image */}
                <img
                  src="/images/cu.png"
                  alt="Crypto"
                  className="w-60 h-60 md:w-68 md:h-68 object-contain drop-shadow-[0_0_15px_rgba(255,255,255,0.3)]"
                />
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Features Showcase Section */}
        <div className="w-full md:w-[982.32px] h-auto mx-auto mt-12 bg-transparent">
          {/* Feature Cards - Mobile Slider */}
          <div className="relative">
            {/* Desktop View - All cards in a row */}
            <div className="hidden md:flex md:flex-row md:gap-2">
              {features.map((feature, index) => {
                // Define different background colors for each feature
                const bgColors = [
                  "bg-gradient-to-br from-blue-600 to-blue-800", // Security
                  "bg-gradient-to-br from-purple-600 to-purple-800", // Support
                  "bg-gradient-to-br from-green-600 to-green-800", // Low fees
                  "bg-gradient-to-br from-orange-600 to-orange-800" // Fast transactions
                ];

                const descriptions = [
                  "با پیشرفته‌ترین سیستم‌های امنیتی، دارایی‌های شما همیشه ایمن خواهند بود",
                  "تیم پشتیبانی ما در تمام ساعات شبانه‌روز آماده پاسخگویی به شماست",
                  "با کمترین کارمزد در بازار، سود بیشتری از معاملات خود کسب کنید",
                  "انجام سریع تراکنش‌ها بدون تاخیر و با بالاترین سرعت ممکن"
                ];

                const icons = [
                  <FaShieldAlt key="shield" className="text-white text-xl" />,
                  <FaHeadset key="headset" className="text-white text-xl" />,
                  <FaPercentage key="percentage" className="text-white text-xl" />,
                  <FaBolt key="bolt" className="text-white text-xl" />
                ];

                return (
                  <div
                    key={index}
                    className={`w-1/4 h-64 p-6 rounded-xl overflow-hidden shadow-lg shadow-black/30 border border-white/10 ${bgColors[index]}`}
                  >
                    <div className="h-full flex flex-col justify-between">
                      <div className="flex items-center gap-4 flex-row-reverse text-right">
                        <div className="w-14 h-14 rounded-full flex items-center justify-center bg-white/20 shadow-inner shadow-black/20">
                          {icons[index]}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-white">
                            {feature}
                          </h3>
                        </div>
                      </div>

                      <div>
                        <p className="text-white/90 mt-1 text-sm">
                          {descriptions[index]}
                        </p>
                        <div className="h-1 bg-white/20 mt-4 rounded-full w-full" />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Mobile View - Single card with auto-slide */}
            <div className="md:hidden">
              {features.map((feature, index) => {
                // Define different background colors for each feature
                const bgColors = [
                  "bg-gradient-to-br from-blue-600 to-blue-800", // Security
                  "bg-gradient-to-br from-purple-600 to-purple-800", // Support
                  "bg-gradient-to-br from-green-600 to-green-800", // Low fees
                  "bg-gradient-to-br from-orange-600 to-orange-800" // Fast transactions
                ];

                const descriptions = [
                  "با پیشرفته‌ترین سیستم‌های امنیتی، دارایی‌های شما همیشه ایمن خواهند بود",
                  "تیم پشتیبانی ما در تمام ساعات شبانه‌روز آماده پاسخگویی به شماست",
                  "با کمترین کارمزد در بازار، سود بیشتری از معاملات خود کسب کنید",
                  "انجام سریع تراکنش‌ها بدون تاخیر و با بالاترین سرعت ممکن"
                ];

                const icons = [
                  <FaShieldAlt key="shield" className="text-white text-xl" />,
                  <FaHeadset key="headset" className="text-white text-xl" />,
                  <FaPercentage key="percentage" className="text-white text-xl" />,
                  <FaBolt key="bolt" className="text-white text-xl" />
                ];

                return (
                  <div
                    key={index}
                    className={`absolute top-0 left-0 w-full h-64 p-6 rounded-xl overflow-hidden shadow-lg shadow-black/30 border border-white/10 transition-opacity duration-500 ${
                      index === activeSlide ? "opacity-100 z-10" : "opacity-0 -z-10"
                    } ${bgColors[index]}`}
                  >
                    <div className="h-full flex flex-col justify-between">
                      <div className="flex items-center gap-4 flex-row-reverse text-right">
                        <div className="w-14 h-14 rounded-full flex items-center justify-center bg-white/20 shadow-inner shadow-black/20">
                          {icons[index]}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-white">
                            {feature}
                          </h3>
                        </div>
                      </div>

                      <div>
                        <p className="text-white/90 mt-1 text-sm">
                          {descriptions[index]}
                        </p>
                        <div className="h-1 bg-white/20 mt-4 rounded-full w-full" />
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Placeholder to maintain height */}
              <div className="w-full h-64 opacity-0"></div>
            </div>

            {/* Slide Indicators - Only visible on mobile */}
            <div className="flex justify-center gap-1 mt-2 md:hidden">
              {features.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === activeSlide
                      ? "bg-blue-500 w-4"
                      : "bg-gray-400/50"
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Search and Start Trading Button */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="mt-8 flex flex-col md:flex-row items-center justify-center gap-4"
          >
          
          </motion.div>
        </div>


      </div>
    </motion.header>
  );
}
