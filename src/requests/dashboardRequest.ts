"use server";
import createAxiosInstance from "@/config/axiosAdapter";

// getting deposit
export async function getDeposit() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/deposit");

    return {
      isError: false,
      data: response.data.data,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// getting profile
export async function getProfile() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/profile");

    const userData = response.data.result;

    return {
      isError: false,
      data: userData,
      status: userData?.status || null,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling send ticket
export async function sendTicket(formData: FormData) {
  try {
    const axiosInstance = await createAxiosInstance();
    await axiosInstance.post("user/support", formData, {
      headers: {
        "content-type": "multipart/form-data",
      },
    });

    return {
      isError: false,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling send ticket reply
export async function sendTicketReply(id: string, message: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post(`user/support/${id}/reply`, {
      message,
    });

    return {
      isError: false,
      status: response.data.status,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// getting deposit
export async function getTickets() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/support");

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// getting ticket detail
export async function getTicketDetail(id: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get(`user/support/${id}`);

    return {
      isError: false,
      data: response.data.result,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// getting cards
export async function getCards() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/card");

    return {
      isError: false,
      data: response.data.result,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling send card
export async function sendCard(number: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    await axiosInstance.post("user/card", { number });

    return {
      isError: false,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// getting user currency
export async function getUserCurrency() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/currency");

    return {
      isError: false,
      data: response.data.result,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling send card
export async function sendDeposit(coin_id: number, network_id: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/deposit/wallet-address", {
      coin_id: coin_id.toString(),
      network_id: network_id.toString(),
    });

    return {
      isError: false,
      message: response.data.result.message,
      data: response.data.result.data,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling read notif
export async function readNotif(id: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post(`user/alert/${id}`, {});

    return {
      isError: false,
      status: response.data.status,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// get withdrawal history
export async function getWithdrawal() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/toman-withdrawal");

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch {
    return {
      isError: true,
    };
  }
}

// handling add withdrawal
export async function addWithdrawal(amount?: string, card_id?: number, code?: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    await axiosInstance.post("user/toman-withdrawal", {
      amount,
      card_id,
      code,
    });

    return {
      isError: false,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response.data.result.message,
      errors: e.response.data.result.errors,
      requires2FA: e.response.data.result.message === "کد احراز هویت دو مرحله‌ای الزامی است"
    };
  }
}

// handling crypto wallet withdrawal
export async function addCryptoWithdrawal(address: string, wallet_id: number, network_id: number, amount: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/wallet/wallet-withdrawal-process", {
      address,
      wallet_id,
      network_id,
      amount,
    });

    return {
      isError: false,
      message: response.data.result?.message || "درخواست برداشت با موفقیت ثبت شد",
      data: response.data.result?.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در ثبت درخواست برداشت",
      errors: e.response?.data?.result?.errors,
    };
  }
}

// handling buy trade
export async function buyTrade(amount: string, currency_id: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/trading/buy", {
      amount,
      currency_id,
    });
    console.log(response);

    return {
      isError: false,
      message: response.data.result.message,
      data: response.data.result.data
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response.data.result.message,
    };
  }
}

// handling sell trade
export async function sellTrade(amount: string, currency_id: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/trading/sell", {
      amount,
      currency_id,
    });
    console.log(response);

    return {
      isError: false,
      message: response.data.result.message,
      data: response.data.result.data
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response.data.result.message,
    };
  }
}

// get transaction totals
export async function getTransactionTotals() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/transaction-totals");

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت اطلاعات تراکنش‌ها",
    };
  }
}

// get trading transactions
export async function getTradingTransactions(page: number = 1, type: string = '') {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get(`user/trading/transactions?page=${page}${type ? `&type=${type}` : ''}`);

    return {
      isError: false,
      data: response.data.result.data,
      pagination: {
        current_page: response.data.result.data.current_page,
        last_page: response.data.result.data.last_page,
        total: response.data.result.data.total,
        per_page: response.data.result.data.per_page,
      }
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت اطلاعات تراکنش‌ها",
    };
  }
}

// handling wallet charge (direct payment)
export async function chargeWallet(amount: string, card_id: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    // TODO: Replace with actual API endpoint when available
    const response = await axiosInstance.post("user/wallet/charge", {
      amount,
      card_id,
    });

    return {
      isError: false,
      message: response.data.result.message,
      data: response.data.result.data
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در شارژ کیف پول",
    };
  }
}

// get transaction details by ID
export async function getTransactionDetails(id: string | number) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get(`user/trading/transaction/${id}`);

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت جزئیات تراکنش",
    };
  }
}

// get daily trading limits
export async function getDailyLimits() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get('user/trading/daily-limits');

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت محدودیت‌های روزانه",
    };
  }
}

// handling swap between currencies
export async function swapCurrency(from_currency_id: number, to_currency_id: number, amount: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/trading/swap", {
      from_currency_id,
      to_currency_id,
      amount,
    });

    return {
      isError: false,
      message: response.data.result.message,
      data: response.data.result.data
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در تبدیل ارز",
    };
  }
}

// get key statistics
export async function getKeyStatistics() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/wallet/key-statistics");

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت آمار کلیدی",
    };
  }
}

// update user national ID
export async function updateNationalId(national_id: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/profile", {
      national_id
    });

    return {
      isError: false,
      message: response.data?.result?.message || "کد ملی با موفقیت ثبت شد و تا لحضاتی دیگر در صورت صحیح تایید خواهد شد.",
      data: response.data?.result?.data
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در بروزرسانی کد ملی",
    };
  }
}

// upload document for authentication
export async function uploadDocument(file: File, name: 'consent' | 'selfie' | 'id' | 'id_back') {
  try {
    const axiosInstance = await createAxiosInstance();
    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', name);

    const response = await axiosInstance.post("user/document", formData, {
      headers: {
        "content-type": "multipart/form-data",
      },
    });

    return {
      isError: false,
      message: response.data?.result?.message || "فایل با موفقیت آپلود شد",
      data: response.data?.result?.data
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در آپلود فایل",
    };
  }
}

// get document verification status
export async function getVerificationStatus() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/document/verification-status");

    return {
      isError: false,
      data: response.data.result?.verification_status,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت وضعیت احراز هویت",
    };
  }
}

// get login history
export async function getLoginHistory(page: number = 1) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/login-history", {
      params: { page }
    });

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت تاریخچه ورود",
    };
  }
}

// get deposit transactions
export async function getDepositTransactions(page: number = 1) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/deposit-transactions", {
      params: { page }
    });
console.log(response.data.result.data);
    return {
      isError: false,
      data: response.data.result.data,
      pagination: response.data.result.data.pagination,
      summary: response.data.result.data.summary,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت تاریخچه واریز",
    };
  }
}

// get deposit transaction details
export async function getDepositTransactionDetails(transactionId: number) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get(`user/deposit-transactions/${transactionId}`);

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت جزییات تراکنش",
    };
  }
}

// get 2FA status
export async function get2FAStatus() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/2fa/status");

    return {
      isError: false,
      data: response.data.result.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت وضعیت احراز هویت دو مرحله‌ای",
    };
  }
}

// generate 2FA secret
export async function generate2FA() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/2fa/generate");

    return {
      isError: false,
      data: response.data.result.data,
      message: response.data.result.message,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در ایجاد کد احراز هویت دو مرحله‌ای",
    };
  }
}

// enable 2FA
export async function enable2FA(code: string, secret: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/2fa/enable", {
      code,
      secret
    });

    return {
      isError: false,
      message: response.data.result.message || "احراز هویت دو مرحله‌ای با موفقیت فعال شد.",
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در فعال‌سازی احراز هویت دو مرحله‌ای",
    };
  }
}

// disable 2FA
export async function disable2FA(code: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/2fa/disable", {
      code
    });

    return {
      isError: false,
      message: response.data.result.message || "احراز هویت دو مرحله‌ای با موفقیت غیرفعال شد.",
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در غیرفعال‌سازی احراز هویت دو مرحله‌ای",
    };
  }
}

// reset 2FA
export async function reset2FA(code: string) {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/2fa/reset", {
      code
    });

    return {
      isError: false,
      message: response.data.result.message || "احراز هویت دو مرحله‌ای با موفقیت بازنشانی شد.",
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در بازنشانی احراز هویت دو مرحله‌ای",
    };
  }
}

// get user level current
export async function getUserLevelCurrent() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/level/current");

    console.log('API Response - Current Level:', response.data);

    return {
      isError: false,
      data: response.data.result?.data || response.data.result || response.data.data,
    };
  } catch (e: any) {
    console.error('API Error - Current Level:', e.response?.data || e.message);
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در دریافت اطلاعات سطح کاربر",
    };
  }
}

// get user level next
export async function getUserLevelNext() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/level/next");

    console.log('API Response - Next Level:', response.data);

    return {
      isError: false,
      data: response.data.result?.data || response.data.data,
    };
  } catch (e: any) {
    console.error('API Error - Next Level:', e.response?.data || e.message);
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در دریافت اطلاعات سطح بعدی",
    };
  }
}

// get user level check status
export async function getUserLevelCheckStatus() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/level/check-status");

    return {
      isError: false,
      data: response.data.result?.data || response.data.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در بررسی وضعیت ارتقا",
    };
  }
}

// get user level history
export async function getUserLevelHistory() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/level/history");

    return {
      isError: false,
      data: response.data.result?.data || response.data.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در دریافت تاریخچه سطوح",
    };
  }
}

// get user level documents
export async function getUserLevelDocuments() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/level/documents");

    return {
      isError: false,
      data: response.data.result?.data || response.data.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در دریافت مدارک سطح",
    };
  }
}

// get user level details
export async function getUserLevelDetails() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/level/check-status");

    console.log('API Response - Level Details:', response.data);

    return {
      isError: false,
      data: response.data.result?.data || response.data.data,
    };
  } catch (e: any) {
    console.error('API Error - Level Details:', e.response?.data || e.message);
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در دریافت جزئیات سطح کاربر",
    };
  }
}

// get user level daily limits
export async function getUserLevelDailyLimits() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/levels/daily-limits");

    console.log('API Response - Daily Limits:', response.data);

    return {
      isError: false,
      data: response.data.result?.data || response.data.data,
    };
  } catch (e: any) {
    console.error('API Error - Daily Limits:', e.response?.data || e.message);
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در دریافت محدودیت‌های روزانه",
    };
  }
}

// request user level upgrade
export async function requestUserLevelUpgrade() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.post("user/level/request-upgrade");

    return {
      isError: false,
      data: response.data.result || response.data,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || e.response?.data?.message || "خطا در درخواست ارتقا سطح",
    };
  }
}

// get referral link
export async function getReferralLink() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/referral/link");

    return {
      isError: false,
      data: response.data.result,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت کد دعوت",
    };
  }
}

// get referral list
export async function getReferralList() {
  try {
    const axiosInstance = await createAxiosInstance();
    const response = await axiosInstance.get("user/referral/list");

    return {
      isError: false,
      data: response.data.result,
    };
  } catch (e: any) {
    return {
      isError: true,
      message: e.response?.data?.result?.message || "خطا در دریافت لیست دعوت‌ها",
    };
  }
}
