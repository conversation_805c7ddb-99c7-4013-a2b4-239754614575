"use client";
import { createContext, useContext, useState, ReactNode } from 'react';

interface UserProfileModalContextType {
  isUserModalOpen: boolean;
  setIsUserModalOpen: (isOpen: boolean) => void;
  openUserModal: () => void;
  closeUserModal: () => void;
}

const UserProfileModalContext = createContext<UserProfileModalContextType | undefined>(undefined);

export const UserProfileModalProvider = ({ children }: { children: ReactNode }) => {
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);

  const openUserModal = () => setIsUserModalOpen(true);
  const closeUserModal = () => setIsUserModalOpen(false);

  return (
    <UserProfileModalContext.Provider value={{
      isUserModalOpen,
      setIsUserModalOpen,
      openUserModal,
      closeUserModal
    }}>
      {children}
    </UserProfileModalContext.Provider>
  );
};

export const useUserProfileModal = () => {
  const context = useContext(UserProfileModalContext);
  if (context === undefined) {
    throw new Error('useUserProfileModal must be used within a UserProfileModalProvider');
  }
  return context;
};
